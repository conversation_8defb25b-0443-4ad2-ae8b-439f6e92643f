<?php

return [

    /*
    |--------------------------------------------------------------------------
    | BBPS API Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains configuration settings for the BBPS API system
    | including authentication keys and other API-related settings.
    |
    */

    /*
    |--------------------------------------------------------------------------
    | Fixed API Keys
    |--------------------------------------------------------------------------
    |
    | These are the fixed API keys that can be used to generate access tokens.
    | In production, these should be stored as environment variables.
    |
    */
    'fixed_api_keys' => [
        env('BBPS_API_KEY_PRIMARY', 'bbps_key_2024_primary'),
        env('BBPS_API_KEY_SECONDARY', 'bbps_key_2024_secondary'),
        env('BBPS_API_KEY_DEV', 'bbps_dev_key_2024'),
    ],

    /*
    |--------------------------------------------------------------------------
    | Token Settings
    |--------------------------------------------------------------------------
    |
    | Configuration for API token generation and management.
    |
    */
    'token' => [
        'default_name' => env('BBPS_DEFAULT_TOKEN_NAME', 'BBPS API Client'),
        'abilities' => ['api-access'], // Default token abilities
        'expires_in_minutes' => env('BBPS_TOKEN_EXPIRES_IN_MINUTES', null), // null = no expiration
    ],

    /*
    |--------------------------------------------------------------------------
    | API Settings
    |--------------------------------------------------------------------------
    |
    | General API configuration settings.
    |
    */
    'api' => [
        'version' => '1.0.0',
        'rate_limit' => env('BBPS_API_RATE_LIMIT', 60), // requests per minute
        'enable_debug_endpoints' => env('BBPS_ENABLE_DEBUG_ENDPOINTS', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | System User Settings
    |--------------------------------------------------------------------------
    |
    | Configuration for the system user used for token generation.
    |
    */
    'system_user' => [
        'email' => env('BBPS_SYSTEM_USER_EMAIL', '<EMAIL>'),
        'name' => env('BBPS_SYSTEM_USER_NAME', 'System API User'),
    ],

];
