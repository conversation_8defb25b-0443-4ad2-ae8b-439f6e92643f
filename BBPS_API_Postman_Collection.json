{"info": {"_postman_id": "bbps-api-collection", "name": "BBPS API Collection", "description": "Collection for testing BBPS Token Authentication API", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "base_url", "value": "http://127.0.0.1:8000/api", "type": "string"}, {"key": "access_token", "value": "", "type": "string"}], "item": [{"name": "Authentication", "item": [{"name": "Get Valid API Keys (Dev Only)", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/auth/valid-keys", "host": ["{{base_url}}"], "path": ["auth", "valid-keys"]}}}, {"name": "Generate Token", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.success && response.data.access_token) {", "        pm.collectionVariables.set('access_token', response.data.access_token);", "        console.log('Access token saved:', response.data.access_token);", "    }", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"api_key\": \"bbps_key_2024_primary\",\n    \"client_name\": \"Postman Test Client\"\n}"}, "url": {"raw": "{{base_url}}/auth/generate-token", "host": ["{{base_url}}"], "path": ["auth", "generate-token"]}}}, {"name": "Validate Token", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/auth/validate-token", "host": ["{{base_url}}"], "path": ["auth", "validate-token"]}}}, {"name": "Revoke Token", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/auth/revoke-token", "host": ["{{base_url}}"], "path": ["auth", "revoke-token"]}}}]}, {"name": "Protected Endpoints", "item": [{"name": "API Status", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/v1/status", "host": ["{{base_url}}"], "path": ["v1", "status"]}}}, {"name": "User Profile", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/v1/profile", "host": ["{{base_url}}"], "path": ["v1", "profile"]}}}, {"name": "Get Data", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/v1/data", "host": ["{{base_url}}"], "path": ["v1", "data"]}}}, {"name": "Create Data", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Test Item from Postman\",\n    \"description\": \"This item was created via Postman\",\n    \"status\": \"active\"\n}"}, "url": {"raw": "{{base_url}}/v1/data", "host": ["{{base_url}}"], "path": ["v1", "data"]}}}, {"name": "Get User Info", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/user", "host": ["{{base_url}}"], "path": ["user"]}}}]}, {"name": "Categories", "item": [{"name": "Get All Categories", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/v1/categories?per_page=10&page=1", "host": ["{{base_url}}"], "path": ["v1", "categories"], "query": [{"key": "per_page", "value": "10"}, {"key": "page", "value": "1"}]}}}, {"name": "Get Active Categories", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/v1/categories/active", "host": ["{{base_url}}"], "path": ["v1", "categories", "active"]}}}, {"name": "Get Categories with <PERSON><PERSON>", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/v1/categories/active?with_billers=true", "host": ["{{base_url}}"], "path": ["v1", "categories", "active"], "query": [{"key": "with_billers", "value": "true"}]}}}, {"name": "Get Categories Statistics", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/v1/categories/stats", "host": ["{{base_url}}"], "path": ["v1", "categories", "stats"]}}}, {"name": "Get Category by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/v1/categories/1", "host": ["{{base_url}}"], "path": ["v1", "categories", "1"]}}}, {"name": "Filter Categories by Status", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/v1/categories?status=1&per_page=20", "host": ["{{base_url}}"], "path": ["v1", "categories"], "query": [{"key": "status", "value": "1"}, {"key": "per_page", "value": "20"}]}}}]}, {"name": "<PERSON><PERSON>", "item": [{"name": "Get Billers by Category ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/v1/billers/category/1?per_page=10&page=1", "host": ["{{base_url}}"], "path": ["v1", "billers", "category", "1"], "query": [{"key": "per_page", "value": "10"}, {"key": "page", "value": "1"}]}}}, {"name": "Get Active Billers by Category", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/v1/billers/category/1?status=1", "host": ["{{base_url}}"], "path": ["v1", "billers", "category", "1"], "query": [{"key": "status", "value": "1"}]}}}, {"name": "Search Billers", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/v1/billers/category/1?search=Krishna", "host": ["{{base_url}}"], "path": ["v1", "billers", "category", "1"], "query": [{"key": "search", "value": "<PERSON>"}]}}}, {"name": "Get <PERSON><PERSON>", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/v1/billers/7", "host": ["{{base_url}}"], "path": ["v1", "billers", "7"]}}}]}, {"name": "Error Testing", "item": [{"name": "Invalid API Key", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"api_key\": \"invalid_key_123\",\n    \"client_name\": \"Test Client\"\n}"}, "url": {"raw": "{{base_url}}/auth/generate-token", "host": ["{{base_url}}"], "path": ["auth", "generate-token"]}}}, {"name": "Invalid <PERSON>", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer invalid_token_123"}], "url": {"raw": "{{base_url}}/v1/status", "host": ["{{base_url}}"], "path": ["v1", "status"]}}}, {"name": "Missing Token", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/v1/status", "host": ["{{base_url}}"], "path": ["v1", "status"]}}}]}]}