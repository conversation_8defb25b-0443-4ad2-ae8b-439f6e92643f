<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\ApiController;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\BillerController;
use App\Http\Controllers\CategoryController;
use App\Http\Controllers\PaymentTransactionController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// Public routes - No authentication required
Route::prefix('auth')->group(function () {
    Route::post('/generate-token', [AuthController::class, 'generateToken']);
    Route::get('/valid-keys', [AuthController::class, 'getValidKeys']); // For development only
});

// Protected routes - Require authentication token
Route::middleware('auth:sanctum')->group(function () {
    // Token management routes
    Route::prefix('auth')->group(function () {
        Route::get('/validate-token', [AuthController::class, 'validateToken']);
        Route::post('/revoke-token', [AuthController::class, 'revokeToken']);
    });

    // User information route
    Route::get('/user', function (Request $request) {
        return $request->user();
    });

    // Protected API endpoints
    Route::prefix('v1')->group(function () {
        Route::get('/status', [ApiController::class, 'status']);
        Route::get('/profile', [ApiController::class, 'profile']);
        Route::get('/data', [ApiController::class, 'getData']);
        Route::post('/data', [ApiController::class, 'createData']);

        // Categories endpoints
        Route::prefix('categories')->group(function () {
            Route::get('/', [CategoryController::class, 'index']);
            Route::get('/active', [CategoryController::class, 'active']);
            Route::get('/stats', [CategoryController::class, 'stats']);
            Route::get('/{id}', [CategoryController::class, 'show']);
        });

        // Billers endpoints
        Route::prefix('billers')->group(function () {
            Route::get('/category/{categoryId}', [BillerController::class, 'getBillersByCategory']);
            Route::get('/{id}', [BillerController::class, 'getBillerDetail']);
            Route::post('/bill/fetch-bill-data', [BillerController::class, 'fetchBillData']);
            Route::get('/bill/fetch-bill-status/{refId}', [BillerController::class, 'getfetchbillstatus']);
        });

        // Payment Transaction endpoints
        Route::prefix('payment-transactions')->group(function () {
            Route::get('/', [PaymentTransactionController::class, 'getPaymentTransaction']);
            Route::post('/', [PaymentTransactionController::class, 'createPaymentTransaction']);
        });
    });
});
