# BBPS Token Authentication API

A Laravel-based API system that implements token-based authentication using fixed API keys. Users can generate access tokens using predefined API keys and then use those tokens to access protected endpoints.

## Features

- **Fixed API Key Authentication**: Use predefined API keys to generate access tokens
- **JWT-like Token System**: Powered by <PERSON><PERSON> Sanctum for secure token management
- **Protected Endpoints**: Sample API endpoints that require authentication
- **Token Management**: Validate and revoke tokens
- **Development Tools**: Debug endpoints for development/testing

## Quick Start

### 1. Installation

```bash
# Install dependencies (if not already done)
composer install

# Run migrations (if not already done)
php artisan migrate

# Start the development server
php artisan serve
```

### 2. Configuration

The BBPS API configuration is already set up in your `.env` file:

```env
# BBPS API Configuration
BBPS_API_KEY_PRIMARY=bbps_key_2024_primary
BBPS_API_KEY_SECONDARY=bbps_key_2024_secondary
BBPS_API_KEY_DEV=bbps_dev_key_2024
BBPS_DEFAULT_TOKEN_NAME="BBPS API Client"
BBPS_TOKEN_EXPIRES_IN_MINUTES=
BBPS_API_RATE_LIMIT=60
BBPS_ENABLE_DEBUG_ENDPOINTS=true
BBPS_SYSTEM_USER_EMAIL=<EMAIL>
BBPS_SYSTEM_USER_NAME="System API User"
```

### 3. Default API Keys (Development)

For development and testing, the following API keys are pre-configured:
- `bbps_key_2024_primary`
- `bbps_key_2024_secondary`
- `bbps_dev_key_2024`

**⚠️ Important**: Change these keys in production!

## API Usage

### Step 1: Generate Access Token

```bash
curl -X POST http://127.0.0.1:8000/api/auth/generate-token \
  -H "Content-Type: application/json" \
  -d '{
    "api_key": "bbps_key_2024_primary",
    "client_name": "My API Client"
  }'
```

**Response:**
```json
{
    "success": true,
    "message": "Token generated successfully",
    "data": {
        "access_token": "1|abcdef123456...",
        "token_type": "Bearer",
        "expires_at": null,
        "client_name": "My API Client"
    }
}
```

### Step 2: Use Token for Protected Endpoints

```bash
curl -X GET http://127.0.0.1:8000/api/v1/status \
  -H "Authorization: Bearer 1|your_access_token_here"

### Step 3: Access Categories API

```bash
# Get all categories (paginated)
curl -X GET http://127.0.0.1:8000/api/v1/categories \
  -H "Authorization: Bearer 1|your_access_token_here"

# Get active categories only
curl -X GET http://127.0.0.1:8000/api/v1/categories/active \
  -H "Authorization: Bearer 1|your_access_token_here"

# Get categories with billers
curl -X GET "http://127.0.0.1:8000/api/v1/categories/active?with_billers=true" \
  -H "Authorization: Bearer 1|your_access_token_here"

# Get categories statistics
curl -X GET http://127.0.0.1:8000/api/v1/categories/stats \
  -H "Authorization: Bearer 1|your_access_token_here"

# Get billers by category ID
curl -X GET http://127.0.0.1:8000/api/v1/billers/category/1 \
  -H "Authorization: Bearer 1|your_access_token_here"

# Search billers in a category
curl -X GET "http://127.0.0.1:8000/api/v1/billers/category/1?search=Krishna" \
  -H "Authorization: Bearer 1|your_access_token_here"

# Get biller detail
curl -X GET http://127.0.0.1:8000/api/v1/billers/7 \
  -H "Authorization: Bearer 1|your_access_token_here"
```
```

## Available Endpoints

### Public Endpoints
- `GET /api/auth/valid-keys` - Get valid API keys (development only)
- `POST /api/auth/generate-token` - Generate access token

### Protected Endpoints (Require Bearer Token)
- `GET /api/auth/validate-token` - Validate current token
- `POST /api/auth/revoke-token` - Revoke current token
- `GET /api/user` - Get authenticated user info
- `GET /api/v1/status` - Get API status
- `GET /api/v1/profile` - Get user profile
- `GET /api/v1/data` - Get sample data
- `POST /api/v1/data` - Create sample data

### Categories Endpoints (Require Bearer Token)
- `GET /api/v1/categories` - Get all categories (paginated)
- `GET /api/v1/categories/active` - Get active categories only
- `GET /api/v1/categories/stats` - Get categories statistics
- `GET /api/v1/categories/{id}` - Get specific category by ID

### Billers Endpoints (Require Bearer Token)
- `GET /api/v1/billers/category/{categoryId}` - Get billers by category ID (paginated)
- `GET /api/v1/billers/{id}` - Get detailed biller information

## Testing

### Automated Testing

Run the included test script:

```bash
php test_api.php
```

This script will test all endpoints and provide detailed output.

### Manual Testing with Postman

Import the included Postman collection:
1. Open Postman
2. Import `BBPS_API_Postman_Collection.json`
3. The collection includes all endpoints with proper authentication setup

### Testing Workflow

1. **Get Valid Keys**: Call `/api/auth/valid-keys` to see available API keys
2. **Generate Token**: Use one of the valid keys to generate an access token
3. **Test Protected Endpoints**: Use the generated token to access protected endpoints
4. **Validate Token**: Verify token is working with `/api/auth/validate-token`
5. **Revoke Token**: Clean up by revoking the token

## File Structure

```
├── app/Http/Controllers/
│   ├── AuthController.php      # Token authentication logic
│   └── ApiController.php       # Sample protected endpoints
├── config/bbps.php            # BBPS API configuration
├── routes/api.php              # API routes definition
├── test_api.php               # Automated test script
├── BBPS_API_Postman_Collection.json  # Postman collection
├── API_DOCUMENTATION.md       # Detailed API documentation
└── BBPS_README.md             # This file
```

## Security Notes

1. **API Keys**: Store API keys securely and rotate them regularly
2. **HTTPS**: Always use HTTPS in production
3. **Environment Variables**: Never commit API keys to version control
4. **Rate Limiting**: Configure appropriate rate limits for your use case
5. **Token Expiration**: Consider setting token expiration times for enhanced security

## Customization

### Adding New Protected Endpoints

1. Create your controller methods
2. Add routes to `routes/api.php` within the `auth:sanctum` middleware group
3. Use `$request->user()` to get the authenticated user

### Modifying API Keys

1. Update the keys in your `.env` file
2. Or modify the `config/bbps.php` file for more complex configurations

### Adding Token Expiration

Set `BBPS_TOKEN_EXPIRES_IN_MINUTES` in your `.env` file to enable token expiration.

## Troubleshooting

### Common Issues

1. **"Unauthenticated" Error**: Check that you're including the `Authorization: Bearer <token>` header
2. **"Invalid API Key" Error**: Verify you're using one of the configured API keys
3. **Database Errors**: Ensure migrations have been run with `php artisan migrate`

### Debug Mode

Debug endpoints are enabled in development. Use `/api/auth/valid-keys` to see available API keys.

## Production Deployment

1. Set `APP_ENV=production` in your `.env` file
2. Update all API keys with secure, unique values
3. Set `BBPS_ENABLE_DEBUG_ENDPOINTS=false`
4. Configure proper database credentials
5. Set up HTTPS
6. Configure rate limiting and monitoring

## Support

For detailed API documentation, see `API_DOCUMENTATION.md`.

For issues or questions, please refer to the Laravel and Laravel Sanctum documentation.
