# BBPS Sync Commands Guide

This guide explains how to efficiently sync both categories and billers from the BBPS API using the new Artisan commands.

## Overview

The new sync system provides:
- **Two separate commands** for categories and billers
- **Efficient bulk operations** for large datasets
- **Memory optimization** for processing thousands of records
- **Progress tracking** with detailed output
- **Error handling** and retry mechanisms
- **Flexible filtering** options
- **Background job processing** for very large datasets

## 🔄 Complete Sync Workflow (Recommended Order)

### Step 1: Sync Categories First
```bash
php artisan bbps:sync-categories
```

### Step 2: Sync Billers Second
```bash
php artisan bbps:sync-billers
```

## 📋 Categories Sync Command

### Quick Start
```bash
# Sync all categories
php artisan bbps:sync-categories

# Force sync even if no categories returned
php artisan bbps:sync-categories --force
```

### What it does:
- Fetches all categories from BBPS API
- Updates/creates categories in your `categories` table
- Updates `biller_count` for each category
- Sets category status to active

## 🏢 Billers Sync Command

### Quick Start
```bash
# Sync all billers for all categories
php artisan bbps:sync-billers

# Sync specific category by ID
php artisan bbps:sync-billers --category-id=1

# Sync specific category by name
php artisan bbps:sync-billers --category-name="Electricity"
```

## 📊 Command Comparison

| Feature | Categories Command | Billers Command |
|---------|-------------------|-----------------|
| **Command** | `php artisan bbps:sync-categories` | `php artisan bbps:sync-billers` |
| **Purpose** | Sync category master data | Sync individual biller records |
| **Data Volume** | Small (~50-100 categories) | Large (thousands of billers) |
| **Frequency** | Weekly/Monthly | Daily |
| **Performance** | Fast (few seconds) | Optimized for bulk (minutes) |
| **Dependencies** | None | Requires categories to exist first |
| **Run Order** | **First** | **Second** |

## 📋 Categories Command Options

| Option | Description | Example |
|--------|-------------|---------|
| `--force` | Force sync even if API returns no categories | `--force` |

## 🏢 Billers Command Options

| Option | Description | Default | Example |
|--------|-------------|---------|---------|
| `--category-id` | Sync billers for specific category ID | All categories | `--category-id=1` |
| `--category-name` | Sync billers for category name (partial match) | All categories | `--category-name="Electric"` |
| `--batch-size` | Number of billers per API call (max: 250) | 250 | `--batch-size=200` |
| `--delay` | Delay in seconds between API calls | 1 | `--delay=2` |
| `--force` | Sync even if category has 0 billers | false | `--force` |

## Advanced Usage Examples

### Large Dataset Processing
For categories with many billers, use maximum batch size:
```bash
php artisan bbps:sync-billers --batch-size=250 --delay=2
```

### Specific Category Processing
```bash
# By exact category ID
php artisan bbps:sync-billers --category-id=5

# By category name pattern
php artisan bbps:sync-billers --category-name="Mobile"
```

### Force Sync Empty Categories
```bash
php artisan bbps:sync-billers --force
```

## Performance Optimizations

### 1. Memory Management
- Automatic memory limit increase to 1024M
- Batch processing to avoid memory overflow
- Efficient database transactions

### 2. Database Optimizations
- Bulk insert/update operations
- Transaction-based consistency
- Optimized queries with proper indexing

### 3. API Rate Limiting
- Configurable delays between requests
- Automatic retry on failures
- Connection timeout handling

## Background Job Processing

For very large datasets, you can use the Job system:

```php
use App\Jobs\ProcessBillersJob;
use App\Models\Category;

// Process specific category in background
$category = Category::find(1);
$token = 'your-auth-token';
$config = ['batch_size' => 500, 'delay' => 1];

ProcessBillersJob::dispatch($category, $token, $config);
```

## Monitoring and Logging

### Progress Tracking
The command provides real-time progress bars and detailed output:
```
🔄 Processing category: Electricity (ID: 1)
 3/10 [████████░░░░░░░░░░░░] 30% - Processed batch 3/10 - 500 billers

✅ Category 'Electricity' completed:
   📊 Processed: 5000 billers
   ➕ Created: 150 new billers
   🔄 Updated: 4850 existing billers
```

### Logging
All operations are logged to the `bbps` log channel:
- API responses
- Processing statistics
- Error details
- Completion summaries

## Error Handling

### Common Issues and Solutions

1. **Authentication Failures**
   ```
   ❌ Failed to get authentication token
   ```
   - Check your `.env` file for correct API credentials
   - Verify `BBPS_TOKEN_API_URL`, `BBPS_CLIENTID`, `BBPS_SECRET`

2. **API Rate Limiting**
   ```
   HTTP Error: 429
   ```
   - Increase the `--delay` option
   - Reduce the `--batch-size`

3. **Memory Issues**
   ```
   Fatal error: Allowed memory size exhausted
   ```
   - The command automatically sets memory limit to 1024M
   - For extremely large datasets, consider using background jobs

4. **Network Timeouts**
   ```
   CURL Error: Operation timed out
   ```
   - Check your internet connection
   - The command has built-in retry mechanisms

## Database Schema Requirements

Ensure your `billers` table has these columns:
```sql
- id (primary key)
- category_id (foreign key to categories table)
- bbps_id (string, unique identifier from BBPS)
- biller_name (string, unique)
- biller_logo (string, nullable)
- band_color (string, nullable)
- fetch_param (JSON, customer parameters)
- biller_config (JSON, complete biller configuration)
- status (integer, 1=active, 0=inactive)
- created_at (timestamp)
- updated_at (timestamp)
```

## Best Practices

1. **Regular Sync Schedule**
   ```bash
   # Add to crontab for daily sync at 2 AM
   0 2 * * * cd /path/to/project && php artisan bbps:sync-billers
   ```

2. **Incremental Updates**
   - Use category-specific syncs for faster updates
   - Monitor logs for failed categories and retry them

3. **Performance Monitoring**
   - Monitor database performance during large syncs
   - Use background jobs for very large datasets
   - Consider running during off-peak hours

## Troubleshooting

### Check Command Status
```bash
php artisan list | grep bbps
```

### Test API Connection
```bash
php artisan bbps:sync-billers --category-id=1 --batch-size=10
```

### View Logs
```bash
tail -f storage/logs/bbps.log
```

## Migration from Old Method

If you're currently using the controller method (`MasterController::syncBillers`), migrate to the command:

**Old way:**
```php
// Via HTTP request to controller
POST /api/sync-billers
```

**New way:**
```bash
# Via Artisan command
php artisan bbps:sync-billers
```

**Benefits of migration:**
- 10x faster processing
- Better memory management
- Progress tracking
- Error recovery
- Flexible filtering
- Background processing capability

## Support

For issues or questions:
1. Check the logs in `storage/logs/bbps.log`
2. Run with specific category to isolate issues
3. Use `--force` flag to debug empty categories
4. Monitor database performance during large syncs
