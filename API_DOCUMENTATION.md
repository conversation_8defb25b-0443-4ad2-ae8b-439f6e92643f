# BBPS API Documentation

## Overview

This API provides a token-based authentication system where you can generate access tokens using fixed API keys and then use those tokens to access protected endpoints.

## Authentication Flow

1. **Generate Token**: Use a fixed API key to generate an access token
2. **Use Token**: Include the access token in the Authorization header for protected endpoints
3. **Manage Token**: Validate or revoke tokens as needed

## Base URL

```
http://your-domain.com/api
```

## Authentication Endpoints

### 1. Generate Access Token

**Endpoint:** `POST /api/auth/generate-token`

**Description:** Generate an access token using a fixed API key.

**Request Body:**
```json
{
    "api_key": "bbps_key_2024_primary",
    "client_name": "My API Client" // Optional
}
```

**Response (Success):**
```json
{
    "success": true,
    "message": "Token generated successfully",
    "data": {
        "access_token": "1|abcdef123456...",
        "token_type": "Bearer",
        "expires_at": null,
        "client_name": "My API Client"
    }
}
```

**Response (Error):**
```json
{
    "success": false,
    "message": "Invalid API key provided"
}
```

### 2. Validate Token

**Endpoint:** `GET /api/auth/validate-token`

**Description:** Validate the current access token and get token information.

**Headers:**
```
Authorization: Bearer 1|abcdef123456...
```

**Response:**
```json
{
    "success": true,
    "message": "Token is valid",
    "data": {
        "user_id": 1,
        "user_name": "System API User",
        "user_email": "<EMAIL>",
        "token_name": "My API Client",
        "abilities": ["api-access"]
    }
}
```

### 3. Revoke Token

**Endpoint:** `POST /api/auth/revoke-token`

**Description:** Revoke the current access token.

**Headers:**
```
Authorization: Bearer 1|abcdef123456...
```

**Response:**
```json
{
    "success": true,
    "message": "Token revoked successfully"
}
```

### 4. Get Valid API Keys (Development Only)

**Endpoint:** `GET /api/auth/valid-keys`

**Description:** Get list of valid API keys for development/testing purposes.

**Note:** This endpoint is only available in non-production environments.

**Response:**
```json
{
    "success": true,
    "message": "Valid API keys for development",
    "data": {
        "valid_keys": [
            "bbps_key_2024_primary",
            "bbps_key_2024_secondary",
            "bbps_dev_key_2024"
        ],
        "note": "Use any of these keys to generate access tokens"
    }
}
```

## Protected Endpoints

All protected endpoints require the `Authorization` header with a valid Bearer token.

**Headers:**
```
Authorization: Bearer 1|abcdef123456...
Content-Type: application/json
```

### 1. API Status

**Endpoint:** `GET /api/v1/status`

**Description:** Get API status and information.

**Response:**
```json
{
    "success": true,
    "message": "BBPS API is running",
    "data": {
        "version": "1.0.0",
        "timestamp": "2024-01-01T12:00:00.000000Z",
        "authenticated_user": "System API User",
        "server_time": "2024-01-01 12:00:00"
    }
}
```

### 2. User Profile

**Endpoint:** `GET /api/v1/profile`

**Description:** Get authenticated user profile.

**Response:**
```json
{
    "success": true,
    "message": "User profile retrieved successfully",
    "data": {
        "id": 1,
        "name": "System API User",
        "email": "<EMAIL>",
        "email_verified_at": "2024-01-01T12:00:00.000000Z",
        "created_at": "2024-01-01T12:00:00.000000Z",
        "updated_at": "2024-01-01T12:00:00.000000Z"
    }
}
```

### 3. Get Data

**Endpoint:** `GET /api/v1/data`

**Description:** Retrieve sample data (replace with your business logic).

**Response:**
```json
{
    "success": true,
    "message": "Data retrieved successfully",
    "data": {
        "items": [
            {
                "id": 1,
                "name": "Sample Item 1",
                "description": "This is a sample item",
                "status": "active",
                "created_at": "2024-01-01T12:00:00.000000Z"
            }
        ],
        "total_count": 1,
        "requested_by": "System API User"
    }
}
```

### 4. Create Data

**Endpoint:** `POST /api/v1/data`

**Description:** Create new data (replace with your business logic).

**Request Body:**
```json
{
    "name": "New Item",
    "description": "Description of the new item",
    "status": "active" // Optional: active or inactive
}
```

**Response:**
```json
{
    "success": true,
    "message": "Data created successfully",
    "data": {
        "id": 1234,
        "name": "New Item",
        "description": "Description of the new item",
        "status": "active",
        "created_by": "System API User",
        "created_at": "2024-01-01T12:00:00.000000Z"
    }
}
```

## Categories Endpoints

### 1. Get All Categories

**Endpoint:** `GET /api/v1/categories`

**Description:** Get paginated list of all categories with optional filtering.

**Query Parameters:**
- `status` (optional): Filter by status (0 = inactive, 1 = active)
- `with_billers` (optional): Filter categories that have billers (true/false)
- `per_page` (optional): Items per page (1-100, default: 50)
- `page` (optional): Page number (default: 1)

**Response:**
```json
{
    "success": true,
    "message": "Categories retrieved successfully",
    "data": {
        "categories": [
            {
                "id": 1,
                "cat_name": "Electricity",
                "cat_logo": "electricity.png",
                "biller_count": 150,
                "status": 1,
                "status_text": "Active",
                "created_at": "2024-01-01T12:00:00.000000Z",
                "updated_at": "2024-01-01T12:00:00.000000Z"
            }
        ],
        "pagination": {
            "current_page": 1,
            "per_page": 50,
            "total": 97,
            "last_page": 2,
            "from": 1,
            "to": 50,
            "has_more_pages": true
        },
        "filters_applied": {
            "status": null,
            "with_billers": false
        },
        "requested_by": "System API User"
    }
}
```

### 2. Get Active Categories

**Endpoint:** `GET /api/v1/categories/active`

**Description:** Get all active categories (status = 1).

**Query Parameters:**
- `with_billers` (optional): Filter categories that have billers (true/false)

**Response:**
```json
{
    "success": true,
    "message": "Active categories retrieved successfully",
    "data": {
        "categories": [
            {
                "id": 1,
                "cat_name": "Electricity",
                "cat_logo": "electricity.png",
                "biller_count": 150,
                "created_at": "2024-01-01T12:00:00.000000Z",
                "updated_at": "2024-01-01T12:00:00.000000Z"
            }
        ],
        "total_count": 97,
        "filters_applied": {
            "status": "active",
            "with_billers": false
        },
        "requested_by": "System API User"
    }
}
```

### 3. Get Category by ID

**Endpoint:** `GET /api/v1/categories/{id}`

**Description:** Get specific category by ID.

**Response:**
```json
{
    "success": true,
    "message": "Category retrieved successfully",
    "data": {
        "category": {
            "id": 1,
            "cat_name": "Electricity",
            "cat_logo": "electricity.png",
            "biller_count": 150,
            "status": 1,
            "status_text": "Active",
            "created_at": "2024-01-01T12:00:00.000000Z",
            "updated_at": "2024-01-01T12:00:00.000000Z"
        },
        "requested_by": "System API User"
    }
}
```

### 4. Get Categories Statistics

**Endpoint:** `GET /api/v1/categories/stats`

**Description:** Get statistical information about categories.

**Response:**
```json
{
    "success": true,
    "message": "Categories statistics retrieved successfully",
    "data": {
        "statistics": {
            "total_categories": 97,
            "active_categories": 97,
            "inactive_categories": 0,
            "categories_with_billers": 66,
            "total_billers": 44880,
            "average_billers_per_category": 462.68
        },
        "requested_by": "System API User",
        "generated_at": "2024-01-01T12:00:00.000000Z"
    }
}
```

## Billers Endpoints

### 1. Get Billers by Category ID

**Endpoint:** `GET /api/v1/billers/category/{categoryId}`

**Description:** Get paginated list of billers for a specific category with optional filtering.

**Path Parameters:**
- `categoryId` (required): The ID of the category

**Query Parameters:**
- `status` (optional): Filter by status (0 = inactive, 1 = active)
- `search` (optional): Search in biller name or BBPS ID
- `per_page` (optional): Items per page (1-100, default: 50)
- `page` (optional): Page number (default: 1)

**Response:**
```json
{
    "success": true,
    "message": "Billers retrieved successfully",
    "data": {
        "category": {
            "id": 1,
            "cat_name": "B2B",
            "status": 1
        },
        "billers": [
            {
                "id": 7,
                "category_id": 1,
                "category_name": "B2B",
                "bbps_id": "APKA00000NATMN",
                "biller_name": "A.P. Kakku Associates",
                "biller_logo": null,
                "band_color": null,
                "status": 1,
                "status_text": "Active",
                "created_at": "2025-02-03T13:17:12.000000Z",
                "updated_at": "2025-02-03T13:17:12.000000Z"
            }
        ],
        "pagination": {
            "current_page": 1,
            "per_page": 50,
            "total": 103,
            "last_page": 3,
            "from": 1,
            "to": 50,
            "has_more_pages": true
        },
        "filters_applied": {
            "category_id": 1,
            "status": null,
            "search": null
        },
        "requested_by": "System API User"
    }
}
```

### 2. Get Biller Detail

**Endpoint:** `GET /api/v1/billers/{id}`

**Description:** Get detailed information about a specific biller including fetch parameters and configuration.

**Path Parameters:**
- `id` (required): The ID of the biller

**Response:**
```json
{
    "success": true,
    "message": "Biller detail retrieved successfully",
    "data": {
        "biller": {
            "id": 7,
            "category_id": 1,
            "category": {
                "id": 1,
                "cat_name": "B2B",
                "cat_logo": null,
                "status": 1,
                "status_text": "Active"
            },
            "bbps_id": "APKA00000NATMN",
            "biller_name": "A.P. Kakku Associates",
            "biller_logo": null,
            "band_color": null,
            "fetch_param": [
                {
                    "dataType": "ALPHANUMERIC",
                    "maxLength": 50,
                    "minLength": 2,
                    "optional": false,
                    "paramName": "Invoice Number",
                    "regex": "^[0-9A-Za-z\/-]{2,50}$",
                    "values": null,
                    "visibility": true
                }
            ],
            "biller_config": {
                "bbpsUpdateTimestamp": "2024-05-16T06:30:03.766+05:30",
                "categoryName": "B2B",
                "country": "IND",
                "coverage": "IND",
                "customerParams": [...],
                "paymentChannels": [...],
                "paymentModes": [...]
            },
            "status": 1,
            "status_text": "Active",
            "created_at": "2025-02-03T13:17:12.000000Z",
            "updated_at": "2025-02-03T13:17:12.000000Z"
        },
        "requested_by": "System API User"
    }
}
```

## Error Responses

All endpoints may return the following error responses:

### 401 Unauthorized
```json
{
    "success": false,
    "message": "Unauthenticated.",
    "error": "Authentication token is required to access this resource."
}
```

### 400 Bad Request
```json
{
    "success": false,
    "message": "Validation failed",
    "errors": {
        "field_name": ["Error message"]
    }
}
```

### 500 Internal Server Error
```json
{
    "success": false,
    "message": "Internal server error",
    "error": "Error details"
}
```

## Configuration

### Environment Variables

Add these variables to your `.env` file:

```env
# BBPS API Configuration
BBPS_API_KEY_PRIMARY=your_primary_key_here
BBPS_API_KEY_SECONDARY=your_secondary_key_here
BBPS_API_KEY_DEV=your_dev_key_here
BBPS_DEFAULT_TOKEN_NAME="BBPS API Client"
BBPS_TOKEN_EXPIRES_IN_MINUTES=
BBPS_API_RATE_LIMIT=60
BBPS_ENABLE_DEBUG_ENDPOINTS=true
BBPS_SYSTEM_USER_EMAIL=<EMAIL>
BBPS_SYSTEM_USER_NAME="System API User"
```

### Default API Keys

For development/testing, the following default keys are configured:
- `bbps_key_2024_primary`
- `bbps_key_2024_secondary`
- `bbps_dev_key_2024`

**Important:** Change these keys in production!

## Usage Examples

### Example 1: Generate Token and Access Protected Endpoint

```bash
# Step 1: Generate token
curl -X POST http://your-domain.com/api/auth/generate-token \
  -H "Content-Type: application/json" \
  -d '{
    "api_key": "bbps_key_2024_primary",
    "client_name": "My Test Client"
  }'

# Response will include access_token

# Step 2: Use token to access protected endpoint
curl -X GET http://your-domain.com/api/v1/status \
  -H "Authorization: Bearer 1|your_access_token_here"
```

### Example 2: Create Data

```bash
curl -X POST http://your-domain.com/api/v1/data \
  -H "Authorization: Bearer 1|your_access_token_here" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Test Item",
    "description": "This is a test item",
    "status": "active"
  }'
```

## Security Notes

1. **Store API keys securely** - Never commit API keys to version control
2. **Use HTTPS** - Always use HTTPS in production
3. **Rotate keys regularly** - Change API keys periodically
4. **Monitor usage** - Keep track of token generation and usage
5. **Rate limiting** - The API includes rate limiting (configurable)

## Next Steps

1. Replace sample endpoints with your actual business logic
2. Add proper validation and error handling for your use cases
3. Implement logging and monitoring
4. Add rate limiting middleware if needed
5. Configure proper CORS settings for your frontend applications
