<?php

namespace App\Console\Commands;

use App\Models\Biller;
use App\Models\Category;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class SyncBillersCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'bbps:sync-billers
                            {--category-id= : Sync billers for specific category ID}
                            {--category-name= : Sync billers for specific category name}
                            {--batch-size=250 : Number of billers to process in each batch (max: 250)}
                            {--delay=1 : Delay in seconds between API calls}
                            {--force : Force sync even if category has no billers}
                            {--debug : Enable debug mode with detailed API logging}
                            {--test-single : Test with single category only}
                            {--skip-invalid : Skip categories with invalid names instead of stopping}
                            {--validate-categories : Check which categories are valid before syncing}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sync billers from BBPS API with efficient bulk operations';

    /**
     * Total billers processed
     */
    private int $totalProcessed = 0;

    /**
     * Total billers created
     */
    private int $totalCreated = 0;

    /**
     * Total billers updated
     */
    private int $totalUpdated = 0;

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🚀 Starting BBPS Billers Sync...');

        // Validate batch size
        $batchSize = (int) $this->option('batch-size');
        if ($batchSize > 250) {
            $this->error('❌ Batch size cannot exceed 250 (API limitation)');
            $this->info('💡 Use --batch-size=250 or lower');
            return Command::FAILURE;
        }

        // Increase memory limit for large datasets
        ini_set('memory_limit', '1024M');
        ini_set('max_execution_time', 0);

        try {
            // Get authentication token
            $token = $this->getSetuToken();
            if (!$token) {
                $this->error('❌ Failed to get authentication token');
                return Command::FAILURE;
            }

            // Get categories to process
            $categories = $this->getCategoriesToProcess();
            if ($categories->isEmpty()) {
                $this->warn('⚠️  No categories found to process');
                return Command::SUCCESS;
            }

            $this->info("📋 Found {$categories->count()} categories to process");

            // Validate categories if requested
            if ($this->option('validate-categories')) {
                return $this->validateCategories($categories, $token);
            }

            // Process each category
            foreach ($categories as $category) {
                $this->processCategory($category, $token);
            }

            // Display summary
            $this->displaySummary();

            return Command::SUCCESS;

        } catch (\Exception $e) {
            $this->error("❌ Error: {$e->getMessage()}");
            Log::error('BBPS Sync Error', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return Command::FAILURE;
        }
    }

    /**
     * Get categories to process based on options
     */
    private function getCategoriesToProcess()
    {
        $query = Category::where('status', 1);

        // Filter by specific category ID
        if ($categoryId = $this->option('category-id')) {
            $query->where('id', $categoryId);
        }

        // Filter by specific category name
        if ($categoryName = $this->option('category-name')) {
            $query->where('cat_name', 'like', "%{$categoryName}%");
        }

        // Only process categories with billers unless forced
        if (!$this->option('force')) {
            $query->where('biller_count', '>', 0);
        }

        // Test mode: only get first category
        if ($this->option('test-single')) {
            return $query->orderBy('cat_name')->limit(1)->get();
        }

        return $query->orderBy('cat_name')->get();
    }

    /**
     * Process billers for a specific category
     */
    private function processCategory(Category $category, string $token)
    {
        $this->info("🔄 Processing category: {$category->cat_name} (ID: {$category->id})");

        $billerCount = $category->biller_count;
        $batchSize = (int) $this->option('batch-size');
        $delay = (int) $this->option('delay');

        if ($billerCount == 0) {
            $this->warn("⚠️  Category '{$category->cat_name}' has no billers to sync");
            return;
        }

        $iterations = ceil($billerCount / $batchSize);
        $after = null;
        $categoryProcessed = 0;
        $categoryCreated = 0;
        $categoryUpdated = 0;

        // Create progress bar
        $progressBar = $this->output->createProgressBar($iterations);
        $progressBar->setFormat(' %current%/%max% [%bar%] %percent:3s%% - %message%');
        $progressBar->setMessage("Fetching batch 1/{$iterations}");

        for ($i = 0; $i < $iterations; $i++) {
            try {
                // Fetch billers from API
                $billersData = $this->fetchBillersFromAPI($category, $token, $batchSize, $after);

                if (empty($billersData['billers'])) {
                    $this->warn("\n⚠️  No billers returned for batch " . ($i + 1));
                    break;
                }

                // Process billers in bulk
                $result = $this->processBillersBatch($billersData['billers'], $category->id);

                $categoryProcessed += $result['processed'];
                $categoryCreated += $result['created'];
                $categoryUpdated += $result['updated'];

                // Update after parameter for next iteration
                $after = $billersData['after'] ?? null;

                // Update progress
                $progressBar->setMessage("Processed batch " . ($i + 1) . "/{$iterations} - {$result['processed']} billers");
                $progressBar->advance();

                // Add delay to avoid rate limiting
                if ($delay > 0 && $i < $iterations - 1) {
                    sleep($delay);
                }

            } catch (\Exception $e) {
                $this->error("\n❌ Error processing batch " . ($i + 1) . ": " . $e->getMessage());

                // Check if it's an invalid category error
                if (strpos($e->getMessage(), 'invalid-category-param') !== false) {
                    $this->warn("⚠️  Category '{$category->cat_name}' is not valid in the API");
                    if ($this->option('skip-invalid')) {
                        $this->info("⏭️  Skipping invalid category and continuing...");
                        break; // Skip to next category
                    }
                }

                Log::error("Batch processing error", [
                    'category' => $category->cat_name,
                    'batch' => $i + 1,
                    'error' => $e->getMessage()
                ]);
                continue;
            }
        }

        $progressBar->finish();

        $this->info("\n✅ Category '{$category->cat_name}' completed:");
        $this->line("   📊 Processed: {$categoryProcessed} billers");
        $this->line("   ➕ Created: {$categoryCreated} new billers");
        $this->line("   🔄 Updated: {$categoryUpdated} existing billers");

        // Update totals
        $this->totalProcessed += $categoryProcessed;
        $this->totalCreated += $categoryCreated;
        $this->totalUpdated += $categoryUpdated;
    }

    /**
     * Fetch billers from BBPS API
     */
    private function fetchBillersFromAPI(Category $category, string $token, int $limit, ?string $after = null): array
    {
        $url = env('BBPS_API_URL') . "/bbps/billers?categoryName=" . urlencode($category->cat_name) . "&limit={$limit}";

        if ($after) {
            $url .= "&after={$after}";
        }

        // Debug: Log the request details
        if ($this->option('debug')) {
            $this->line("🔍 Debug: Requesting URL: {$url}");
        }

        $curl = curl_init();
        curl_setopt_array($curl, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => 60,
            CURLOPT_CONNECTTIMEOUT => 30,
            CURLOPT_HTTPHEADER => [
                'X-PARTNER-ID: ' . env('BBPS_X_PARTNER_ID'),
                'Accept: application/json',
                'Authorization: Bearer ' . $token,
            ],
        ]);

        $response = curl_exec($curl);
        $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
        $error = curl_error($curl);
        curl_close($curl);

        if ($error) {
            throw new \Exception("CURL Error: {$error}");
        }

        // Enhanced error handling with response details
        if ($httpCode !== 200) {
            $this->error("🚨 API Request Failed:");
            $this->line("   URL: {$url}");
            $this->line("   HTTP Code: {$httpCode}");
            $this->line("   Response: " . substr($response, 0, 500));

            // Log detailed error information
            Log::channel('bbps')->error("API Request Failed", [
                'category' => $category->cat_name,
                'url' => $url,
                'http_code' => $httpCode,
                'response' => $response
            ]);

            throw new \Exception("HTTP Error: {$httpCode} - Response: " . substr($response, 0, 200));
        }

        $responseArray = json_decode($response, true);

        if (!$responseArray || !isset($responseArray['success']) || !$responseArray['success']) {
            $this->error("🚨 API Response Error:");
            $this->line("   Response: " . substr($response, 0, 500));

            Log::channel('bbps')->error("Invalid API Response", [
                'category' => $category->cat_name,
                'response' => $response
            ]);

            throw new \Exception("API Error: Invalid response - " . substr($response, 0, 200));
        }

        if (!isset($responseArray['data']['billers'])) {
            return ['billers' => [], 'after' => null];
        }

        $billers = $responseArray['data']['billers'];
        $lastBiller = end($billers);
        $after = $lastBiller ? $lastBiller['id'] : null;

        // Log API response for debugging
        Log::channel('bbps')->info("Fetched billers for {$category->cat_name}", [
            'count' => count($billers),
            'after' => $after
        ]);

        return [
            'billers' => $billers,
            'after' => $after
        ];
    }

    /**
     * Process billers batch with efficient bulk operations
     */
    private function processBillersBatch(array $billers, int $categoryId): array
    {
        $processed = 0;
        $created = 0;
        $updated = 0;
        $batchData = [];

        // Prepare data for bulk operations
        foreach ($billers as $biller) {
            $billerData = [
                'biller_name' => trim($biller['name']),
                'bbps_id' => $biller['id'],
                'fetch_param' => $biller['customerParams'] ?? [], // Store as array, not JSON string
                'category_id' => $categoryId,
                'biller_config' => $biller, // Store as array, not JSON string
                'biller_logo' => $biller['logo'] ?? null,
                'status' => 1,
                'updated_at' => now(),
            ];

            $batchData[] = $billerData;
            $processed++;
        }

        if (empty($batchData)) {
            return ['processed' => 0, 'created' => 0, 'updated' => 0];
        }

        // Use database transaction for consistency
        DB::transaction(function () use ($batchData, &$created, &$updated) {
            foreach ($batchData as $data) {
                // Fix JSON encoding issues - store as arrays, not JSON strings
                $data['fetch_param'] = is_string($data['fetch_param']) ?
                    json_decode($data['fetch_param'], true) : $data['fetch_param'];
                $data['biller_config'] = is_string($data['biller_config']) ?
                    json_decode($data['biller_config'], true) : $data['biller_config'];

                $biller = Biller::where('biller_name', $data['biller_name'])->first();

                if ($biller) {
                    // Update existing biller
                    $biller->update($data);
                    $updated++;
                } else {
                    // Create new biller
                    $data['created_at'] = now();
                    Biller::create($data);
                    $created++;
                }
            }
        });

        return [
            'processed' => $processed,
            'created' => $created,
            'updated' => $updated
        ];
    }

    /**
     * Get SETU authentication token
     */
    private function getSetuToken(): ?string
    {
        try {
            $curl = curl_init();
            curl_setopt_array($curl, [
                CURLOPT_URL => env('BBPS_TOKEN_API_URL') . '/auth/token',
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_TIMEOUT => 30,
                CURLOPT_SSL_VERIFYPEER => false,
                CURLOPT_CUSTOMREQUEST => 'POST',
                CURLOPT_POSTFIELDS => json_encode([
                    'clientID' => env('BBPS_CLIENTID'),
                    'secret' => env('BBPS_SECRET')
                ]),
                CURLOPT_HTTPHEADER => [
                    'X-PARTNER-ID: ' . env('BBPS_X_PARTNER_ID'),
                    'Content-Type: application/json'
                ],
            ]);

            $response = curl_exec($curl);
            $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
            $error = curl_error($curl);
            curl_close($curl);

            if ($error) {
                $this->error("CURL Error getting token: {$error}");
                return null;
            }

            if ($httpCode !== 200) {
                $this->error("HTTP Error getting token: {$httpCode}");
                return null;
            }

            $responseArray = json_decode($response, true);

            if (!$responseArray || !isset($responseArray['success']) || !$responseArray['success']) {
                $this->error('Failed to get valid token response');
                return null;
            }

            $this->info('✅ Authentication token obtained successfully');
            return $responseArray['token'] ?? null;

        } catch (\Exception $e) {
            $this->error("Exception getting token: {$e->getMessage()}");
            return null;
        }
    }

    /**
     * Display sync summary
     */
    private function displaySummary()
    {
        $this->info("\n" . str_repeat('=', 60));
        $this->info('🎉 BBPS Billers Sync Completed Successfully!');
        $this->info(str_repeat('=', 60));
        $this->line("📊 Total Processed: {$this->totalProcessed} billers");
        $this->line("➕ Total Created: {$this->totalCreated} new billers");
        $this->line("🔄 Total Updated: {$this->totalUpdated} existing billers");
        $this->info(str_repeat('=', 60));

        // Log summary
        Log::channel('bbps')->info('BBPS Sync Summary', [
            'total_processed' => $this->totalProcessed,
            'total_created' => $this->totalCreated,
            'total_updated' => $this->totalUpdated,
            'completed_at' => now()
        ]);
    }

    /**
     * Validate which categories are valid in the API
     */
    private function validateCategories($categories, string $token): int
    {
        $this->info('🔍 Validating category names with BBPS API...');

        $validCategories = [];
        $invalidCategories = [];

        // Create progress bar
        $progressBar = $this->output->createProgressBar($categories->count());
        $progressBar->setFormat(' %current%/%max% [%bar%] %percent:3s%% - %message%');
        $progressBar->setMessage('Validating categories...');

        foreach ($categories as $category) {
            try {
                // Try to fetch just 1 biller to test if category name is valid
                $url = env('BBPS_API_URL') . "/bbps/billers?categoryName=" . urlencode($category->cat_name) . "&limit=1";

                $curl = curl_init();
                curl_setopt_array($curl, [
                    CURLOPT_URL => $url,
                    CURLOPT_RETURNTRANSFER => true,
                    CURLOPT_TIMEOUT => 30,
                    CURLOPT_HTTPHEADER => [
                        'X-PARTNER-ID: ' . env('BBPS_X_PARTNER_ID'),
                        'Accept: application/json',
                        'Authorization: Bearer ' . $token,
                    ],
                ]);

                $response = curl_exec($curl);
                $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
                curl_close($curl);

                if ($httpCode === 200) {
                    $responseArray = json_decode($response, true);
                    if ($responseArray && isset($responseArray['success']) && $responseArray['success']) {
                        $validCategories[] = $category;
                        $progressBar->setMessage("✅ {$category->cat_name}");
                    } else {
                        $invalidCategories[] = $category;
                        $progressBar->setMessage("❌ {$category->cat_name}");
                    }
                } else {
                    $invalidCategories[] = $category;
                    $progressBar->setMessage("❌ {$category->cat_name}");
                }

                $progressBar->advance();
                sleep(0.5); // Small delay to avoid rate limiting

            } catch (\Exception $e) {
                $invalidCategories[] = $category;
                $progressBar->setMessage("❌ {$category->cat_name}");
                $progressBar->advance();
            }
        }

        $progressBar->finish();
        $this->line('');

        // Display results
        $this->info("\n" . str_repeat('=', 60));
        $this->info('📊 Category Validation Results');
        $this->info(str_repeat('=', 60));

        $this->info("✅ Valid Categories (" . count($validCategories) . "):");
        foreach ($validCategories as $category) {
            $this->line("   • {$category->cat_name} (ID: {$category->id}, Billers: {$category->biller_count})");
        }

        if (!empty($invalidCategories)) {
            $this->warn("\n❌ Invalid Categories (" . count($invalidCategories) . "):");
            foreach ($invalidCategories as $category) {
                $this->line("   • {$category->cat_name} (ID: {$category->id})");
            }

            $this->info("\n💡 Suggestions:");
            $this->line("   • Use --skip-invalid flag to skip invalid categories during sync");
            $this->line("   • Check if category names need to be updated in your database");
            $this->line("   • Some categories might not have billers in the API");
        }

        $this->info(str_repeat('=', 60));

        return Command::SUCCESS;
    }
}
