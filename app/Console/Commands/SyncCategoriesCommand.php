<?php

namespace App\Console\Commands;

use App\Models\Category;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class SyncCategoriesCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'bbps:sync-categories
                            {--force : Force sync even if API returns no categories}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sync categories from BBPS API';

    /**
     * Total categories processed
     */
    private int $totalProcessed = 0;

    /**
     * Total categories created
     */
    private int $totalCreated = 0;

    /**
     * Total categories updated
     */
    private int $totalUpdated = 0;

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🚀 Starting BBPS Categories Sync...');

        try {
            // Get authentication token
            $token = $this->getSetuToken();
            if (!$token) {
                $this->error('❌ Failed to get authentication token');
                return Command::FAILURE;
            }

            // Fetch categories from API
            $categoriesData = $this->fetchCategoriesFromAPI($token);

            if (empty($categoriesData)) {
                $message = '⚠️  No categories returned from API';
                if (!$this->option('force')) {
                    $this->warn($message);
                    return Command::SUCCESS;
                }
                $this->warn($message . ' (continuing due to --force flag)');
                return Command::SUCCESS;
            }

            $this->info("📋 Found " . count($categoriesData) . " categories to process");

            // Process categories
            $this->processCategories($categoriesData);

            // Display summary
            $this->displaySummary();

            return Command::SUCCESS;

        } catch (\Exception $e) {
            $this->error("❌ Error: {$e->getMessage()}");
            Log::error('BBPS Categories Sync Error', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return Command::FAILURE;
        }
    }

    /**
     * Fetch categories from BBPS API
     */
    private function fetchCategoriesFromAPI(string $token): array
    {
        $this->info('🔄 Fetching categories from BBPS API...');

        $curl = curl_init();
        curl_setopt_array($curl, [
            CURLOPT_URL => env('BBPS_API_URL') . '/bbps/categories',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'GET',
            CURLOPT_HTTPHEADER => [
                'X-PARTNER-ID: ' . env('BBPS_X_PARTNER_ID'),
                'Accept: application/json',
                'Authorization: Bearer ' . $token
            ],
        ]);

        $response = curl_exec($curl);
        $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
        $error = curl_error($curl);
        curl_close($curl);

        if ($error) {
            throw new \Exception("CURL Error: {$error}");
        }

        if ($httpCode !== 200) {
            throw new \Exception("HTTP Error: {$httpCode}");
        }

        $responseArray = json_decode($response, true);

        if (!$responseArray || !isset($responseArray['success']) || !$responseArray['success']) {
            throw new \Exception("API Error: Invalid response");
        }

        if (!isset($responseArray['data']['categories'])) {
            return [];
        }

        // Log API response for debugging
        Log::channel('bbps')->info('Fetched categories from API', [
            'count' => count($responseArray['data']['categories'])
        ]);

        return $responseArray['data']['categories'];
    }

    /**
     * Process categories and update database
     */
    private function processCategories(array $categories): void
    {
        $this->info('💾 Processing categories...');

        // Create progress bar
        $progressBar = $this->output->createProgressBar(count($categories));
        $progressBar->setFormat(' %current%/%max% [%bar%] %percent:3s%% - %message%');
        $progressBar->setMessage('Processing categories...');

        foreach ($categories as $categoryData) {
            try {
                $categoryName = trim($categoryData['name']);
                $billerCount = $categoryData['billerCount'] ?? 0;

                // Check if category exists
                $category = Category::where('cat_name', $categoryName)->first();

                if ($category) {
                    // Update existing category
                    $category->update([
                        'biller_count' => $billerCount,
                        'status' => 1
                    ]);
                    $this->totalUpdated++;
                    $progressBar->setMessage("Updated: {$categoryName}");
                } else {
                    // Create new category
                    Category::create([
                        'cat_name' => $categoryName,
                        'biller_count' => $billerCount,
                        'status' => 1
                    ]);
                    $this->totalCreated++;
                    $progressBar->setMessage("Created: {$categoryName}");
                }

                $this->totalProcessed++;
                $progressBar->advance();

            } catch (\Exception $e) {
                $this->error("\n❌ Error processing category: " . $e->getMessage());
                Log::error("Category processing error", [
                    'category' => $categoryData['name'] ?? 'Unknown',
                    'error' => $e->getMessage()
                ]);
                continue;
            }
        }

        $progressBar->finish();
        $this->line('');
    }

    /**
     * Get SETU authentication token
     */
    private function getSetuToken(): ?string
    {
        try {
            $this->info('🔐 Obtaining authentication token...');

            $curl = curl_init();
            curl_setopt_array($curl, [
                CURLOPT_URL => env('BBPS_TOKEN_API_URL') . '/auth/token',
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_TIMEOUT => 30,
                CURLOPT_SSL_VERIFYPEER => false,
                CURLOPT_CUSTOMREQUEST => 'POST',
                CURLOPT_POSTFIELDS => json_encode([
                    'clientID' => env('BBPS_CLIENTID'),
                    'secret' => env('BBPS_SECRET')
                ]),
                CURLOPT_HTTPHEADER => [
                    'X-PARTNER-ID: ' . env('BBPS_X_PARTNER_ID'),
                    'Content-Type: application/json'
                ],
            ]);

            $response = curl_exec($curl);
            $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
            $error = curl_error($curl);
            curl_close($curl);

            if ($error) {
                $this->error("CURL Error getting token: {$error}");
                return null;
            }

            if ($httpCode !== 200) {
                $this->error("HTTP Error getting token: {$httpCode}");
                return null;
            }

            $responseArray = json_decode($response, true);

            if (!$responseArray || !isset($responseArray['success']) || !$responseArray['success']) {
                $this->error('Failed to get valid token response');
                return null;
            }

            $this->info('✅ Authentication token obtained successfully');
            return $responseArray['token'] ?? null;

        } catch (\Exception $e) {
            $this->error("Exception getting token: {$e->getMessage()}");
            return null;
        }
    }

    /**
     * Display sync summary
     */
    private function displaySummary(): void
    {
        $this->info("\n" . str_repeat('=', 50));
        $this->info('🎉 BBPS Categories Sync Completed Successfully!');
        $this->info(str_repeat('=', 50));
        $this->line("📊 Total Processed: {$this->totalProcessed} categories");
        $this->line("➕ Total Created: {$this->totalCreated} new categories");
        $this->line("🔄 Total Updated: {$this->totalUpdated} existing categories");
        $this->info(str_repeat('=', 50));

        // Log summary
        Log::channel('bbps')->info('BBPS Categories Sync Summary', [
            'total_processed' => $this->totalProcessed,
            'total_created' => $this->totalCreated,
            'total_updated' => $this->totalUpdated,
            'completed_at' => now()
        ]);
    }
}
