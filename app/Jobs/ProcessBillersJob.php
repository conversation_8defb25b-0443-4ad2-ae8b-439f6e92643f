<?php

namespace App\Jobs;

use App\Models\Biller;
use App\Models\Category;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ProcessBillersJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * The number of times the job may be attempted.
     */
    public int $tries = 3;

    /**
     * The maximum number of seconds the job can run.
     */
    public int $timeout = 300;

    /**
     * Category to process
     */
    private Category $category;

    /**
     * Authentication token
     */
    private string $token;

    /**
     * Batch configuration
     */
    private array $config;

    /**
     * Create a new job instance.
     */
    public function __construct(Category $category, string $token, array $config = [])
    {
        $this->category = $category;
        $this->token = $token;
        $this->config = array_merge([
            'batch_size' => 250,
            'delay' => 1,
        ], $config);
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            Log::info("Processing billers for category: {$this->category->cat_name}");

            $billerCount = $this->category->biller_count;
            $batchSize = $this->config['batch_size'];
            $delay = $this->config['delay'];

            if ($billerCount == 0) {
                Log::info("Category '{$this->category->cat_name}' has no billers to sync");
                return;
            }

            $iterations = ceil($billerCount / $batchSize);
            $after = null;
            $totalProcessed = 0;

            for ($i = 0; $i < $iterations; $i++) {
                // Fetch billers from API
                $billersData = $this->fetchBillersFromAPI($batchSize, $after);

                if (empty($billersData['billers'])) {
                    Log::warning("No billers returned for batch " . ($i + 1) . " in category {$this->category->cat_name}");
                    break;
                }

                // Process billers in bulk
                $processed = $this->processBillersBatch($billersData['billers']);
                $totalProcessed += $processed;

                // Update after parameter for next iteration
                $after = $billersData['after'] ?? null;

                // Add delay to avoid rate limiting
                if ($delay > 0 && $i < $iterations - 1) {
                    sleep($delay);
                }
            }

            Log::info("Completed processing category '{$this->category->cat_name}': {$totalProcessed} billers processed");

        } catch (\Exception $e) {
            Log::error("Error processing billers for category {$this->category->cat_name}: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Fetch billers from BBPS API
     */
    private function fetchBillersFromAPI(int $limit, ?string $after = null): array
    {
        $url = env('BBPS_API_URL') . "/bbps/billers?categoryName=" . urlencode($this->category->cat_name) . "&limit={$limit}";

        if ($after) {
            $url .= "&after={$after}";
        }

        $curl = curl_init();
        curl_setopt_array($curl, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => 60,
            CURLOPT_CONNECTTIMEOUT => 30,
            CURLOPT_HTTPHEADER => [
                'X-PARTNER-ID: ' . env('BBPS_X_PARTNER_ID'),
                'Accept: application/json',
                'Authorization: Bearer ' . $this->token,
            ],
        ]);

        $response = curl_exec($curl);
        $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
        $error = curl_error($curl);
        curl_close($curl);

        if ($error) {
            throw new \Exception("CURL Error: {$error}");
        }

        if ($httpCode !== 200) {
            throw new \Exception("HTTP Error: {$httpCode}");
        }

        $responseArray = json_decode($response, true);

        if (!$responseArray || !isset($responseArray['success']) || !$responseArray['success']) {
            throw new \Exception("API Error: Invalid response");
        }

        if (!isset($responseArray['data']['billers'])) {
            return ['billers' => [], 'after' => null];
        }

        $billers = $responseArray['data']['billers'];
        $lastBiller = end($billers);
        $after = $lastBiller ? $lastBiller['id'] : null;

        return [
            'billers' => $billers,
            'after' => $after
        ];
    }

    /**
     * Process billers batch with efficient bulk operations
     */
    private function processBillersBatch(array $billers): int
    {
        $processed = 0;

        // Use database transaction for consistency
        DB::transaction(function () use ($billers, &$processed) {
            foreach ($billers as $biller) {
                $billerData = [
                    'biller_name' => trim($biller['name']),
                    'bbps_id' => $biller['id'],
                    'fetch_param' => $biller['customerParams'] ?? [], // Store as array
                    'category_id' => $this->category->id,
                    'biller_config' => $biller, // Store as array
                    'biller_logo' => $biller['logo'] ?? null,
                    'status' => 1,
                    'updated_at' => now(),
                ];

                $existingBiller = Biller::where('biller_name', $billerData['biller_name'])->first();

                if ($existingBiller) {
                    $existingBiller->update($billerData);
                } else {
                    $billerData['created_at'] = now();
                    Biller::create($billerData);
                }

                $processed++;
            }
        });

        return $processed;
    }
}
