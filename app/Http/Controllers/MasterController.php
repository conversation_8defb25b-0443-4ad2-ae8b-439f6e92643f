<?php

namespace App\Http\Controllers;

use App\Models\Biller;
use App\Models\Category;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class MasterController extends Controller
{
     public function syncCategories(Request $request)
    {
        $tokenData=$this->getSetuToken();
        if ($tokenData && isset($tokenData['success'])) {
            $token=$tokenData['token'];
        $curl = curl_init();
        curl_setopt_array($curl, array(
        CURLOPT_URL => env('BBPS_API_URL').'/bbps/categories',
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING => '',
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 0,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => 'GET',
        CURLOPT_HTTPHEADER => array(
            'X-PARTNER-ID: '.env('BBPS_X_PARTNER_ID'),
            'Accept: application/json',
            'Authorization: Bearer '.$token
        ),
        ));

        $response = curl_exec($curl);

        curl_close($curl);
        $responseArray = json_decode($response, true);
            if ($responseArray && isset($responseArray['success']) && @$responseArray['success']) {
                if(isset($responseArray['data']['categories']) ) {
                        foreach($responseArray['data']['categories'] as $category){
                           // dd($category);
                            Category::updateOrCreate(['cat_name'=> trim($category['name'])],['biller_count'=>$category['billerCount']],['status'=> 1]);
                        }
                }
            }
        }
    }
    public function syncBillers(Request $request)
    {
        ini_set('memory_limit', '512M');
        $categories = Category::where('status', 1)->get();
        $tokenData = $this->getSetuToken();

        if ($tokenData && isset($tokenData['success'])) {
            $token = $tokenData['token'];

            foreach ($categories as $category) {
                $billerCount = $category->biller_count;
        $limit = 1000;
        $iterations = ceil($billerCount / $limit);
        $after = null;

        for ($i = 0; $i < $iterations; $i++) {
            $url = env('BBPS_API_URL') . "/bbps/billers?categoryName=" . urlencode($category->cat_name) . "&limit={$limit}";
            if ($after) {
                $url .= "&after={$after}";
            }

            $curl = curl_init();
            curl_setopt_array($curl, [
                CURLOPT_URL => $url,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_TIMEOUT => 60,
                CURLOPT_HTTPHEADER => [
                    'X-PARTNER-ID: ' . env('BBPS_X_PARTNER_ID'),
                    'Accept: application/json',
                    'Authorization: Bearer ' . $token,
                ],
            ]);

            $response = curl_exec($curl);
            curl_close($curl);

            Log::channel('bbps')->info("Bills for {$category->cat_name}: " . $response);

            $responseArray = json_decode($response, true);
            if ($responseArray && isset($responseArray['success']) && $responseArray['success']) {
                if (isset($responseArray['data']['billers'])) {
                    foreach ($responseArray['data']['billers'] as $key => $biller) {
                        Biller::updateOrCreate(
                            ['biller_name' => trim($biller['name'])],
                            [
                                'bbps_id' => $biller['id'],
                                'fetch_param' => $biller['customerParams'] ?? [], // Store as array
                                'category_id' => $category->id,
                                'biller_config' => $biller, // Store as array
                                'biller_logo' => $biller['logo'] ?? null,
                                'status' => 1
                            ]
                        );

                        $after = $biller['id'];
                    }
                }
            }

            sleep(1); // Avoid hitting API rate limits
        }
                // ProcessBillers::dispatch($category, $token);
            }

            return response()->json(['message' => 'Billers processing started in background.'], 200);
        }

        return response()->json(['error' => 'Token retrieval failed.'], 400);
        // ini_set('max_execution_time', 3000);
        // $categories = Category::where('status', 1)->get();
        // foreach ($categories as $category) {
        //     $tokenData = $this->getSetuToken();
        //     if ($tokenData && isset($tokenData['success'])) {
        //         $token = $tokenData['token'];
        //         $billerCount = $category->biller_count; // Total billers
        //         $limit = 1000; // Fetch 1000 per request
        //         $iterations = ceil($billerCount / $limit);
        //         $after = null;

        //         for ($i = 0; $i < $iterations; $i++) {
        //             $curl = curl_init();
        //             $encodedCategoryName = urlencode($category->cat_name);
        //             $url = env('BBPS_API_URL') . "/bbps/billers?categoryName={$encodedCategoryName}&limit={$limit}";

        //             // Add "after" parameter for pagination
        //             if ($after) {
        //                 $url .= "&after={$after}";
        //             }

        //             curl_setopt_array($curl, [
        //                 CURLOPT_URL => $url,
        //                 CURLOPT_RETURNTRANSFER => true,
        //                 CURLOPT_TIMEOUT => 60, // Avoid timeout
        //                 CURLOPT_HTTPHEADER => [
        //                     'X-PARTNER-ID: ' . env('BBPS_X_PARTNER_ID'),
        //                     'Accept: application/json',
        //                     'Authorization: Bearer ' . $token,
        //                 ],
        //             ]);

        //             $response = curl_exec($curl);
        //             curl_close($curl);

        //             Log::channel('bbps')->info("Bills for {$category->cat_name}: " . $response);

        //             $responseArray = json_decode($response, true);
        //             if ($responseArray && isset($responseArray['success']) && $responseArray['success']) {
        //                 if (isset($responseArray['data']['billers'])) {
        //                     foreach ($responseArray['data']['billers'] as $key => $biller) {
        //                         Biller::updateOrCreate(
        //                             ['biller_name' => trim($biller['name'])],
        //                             [
        //                                 'bbps_id' => $biller['id'],
        //                                 'featch_param' => json_encode($biller['customerParams']),
        //                                 'category_id' => $category->id,
        //                                 'biller_config' => json_encode($biller),
        //                                 'biller_logo' => $biller['logo'] ?? null,
        //                                 'status' => 1
        //                             ]
        //                         );

        //                         // Set "after" parameter for next API call
        //                         $after = $biller['id'];
        //                     }
        //                 }
        //             }

        //             // Prevent execution time issues by delaying between requests
        //             sleep(1); // 1-second delay
        //         }
        //     }
        // }


    }
    public function getSetuToken(){
        $curl = curl_init();
        curl_setopt_array($curl, array(
        CURLOPT_URL => env('BBPS_TOKEN_API_URL').'/auth/token',
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING => '',
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 0,
        CURLOPT_SSL_VERIFYPEER=>false,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => 'POST',
        CURLOPT_POSTFIELDS =>'{  "clientID": "'.env('BBPS_CLIENTID').'",  "secret": "'.env('BBPS_SECRET').'"}',
        CURLOPT_HTTPHEADER => array(
            'X-PARTNER-ID: '.env('BBPS_X_PARTNER_ID'),
            'Content-Type: application/json'
        ),
        ));
        if(curl_exec($curl) === false)
        {
            echo 'Curl error: ' . curl_error($curl);
        }
        $response = curl_exec($curl);

        curl_close($curl);

        $responseArray = json_decode($response, true);


        return  $responseArray ;
    }
}
