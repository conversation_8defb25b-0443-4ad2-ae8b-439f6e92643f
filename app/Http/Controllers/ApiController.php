<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class ApiController extends Controller
{
    /**
     * Get API status and information
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function status(Request $request): JsonResponse
    {
        return response()->json([
            'success' => true,
            'message' => 'BBPS API is running',
            'data' => [
                'version' => '1.0.0',
                'timestamp' => now()->toISOString(),
                'authenticated_user' => $request->user()->name ?? 'Unknown',
                'server_time' => now()->format('Y-m-d H:i:s')
            ]
        ], 200);
    }

    /**
     * Sample protected endpoint - Get user profile
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function profile(Request $request): JsonResponse
    {
        $user = $request->user();
        
        return response()->json([
            'success' => true,
            'message' => 'User profile retrieved successfully',
            'data' => [
                'id' => $user->id,
                'name' => $user->name,
                'email' => $user->email,
                'email_verified_at' => $user->email_verified_at,
                'created_at' => $user->created_at,
                'updated_at' => $user->updated_at
            ]
        ], 200);
    }

    /**
     * Sample protected endpoint - Get data
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getData(Request $request): JsonResponse
    {
        // Sample data - replace with your actual business logic
        $sampleData = [
            [
                'id' => 1,
                'name' => 'Sample Item 1',
                'description' => 'This is a sample item',
                'status' => 'active',
                'created_at' => now()->subDays(5)->toISOString()
            ],
            [
                'id' => 2,
                'name' => 'Sample Item 2',
                'description' => 'This is another sample item',
                'status' => 'inactive',
                'created_at' => now()->subDays(3)->toISOString()
            ],
            [
                'id' => 3,
                'name' => 'Sample Item 3',
                'description' => 'This is yet another sample item',
                'status' => 'active',
                'created_at' => now()->subDays(1)->toISOString()
            ]
        ];

        return response()->json([
            'success' => true,
            'message' => 'Data retrieved successfully',
            'data' => [
                'items' => $sampleData,
                'total_count' => count($sampleData),
                'requested_by' => $request->user()->name
            ]
        ], 200);
    }

    /**
     * Sample protected endpoint - Create data
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function createData(Request $request): JsonResponse
    {
        $validator = \Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'description' => 'required|string|max:1000',
            'status' => 'in:active,inactive'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 400);
        }

        // Sample creation logic - replace with your actual business logic
        $newItem = [
            'id' => rand(1000, 9999),
            'name' => $request->input('name'),
            'description' => $request->input('description'),
            'status' => $request->input('status', 'active'),
            'created_by' => $request->user()->name,
            'created_at' => now()->toISOString()
        ];

        return response()->json([
            'success' => true,
            'message' => 'Data created successfully',
            'data' => $newItem
        ], 201);
    }
}
