<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use App\Models\PaymentTransaction;
use Illuminate\Support\Facades\Validator;

class PaymentTransactionController extends Controller
{
    /**
     * Get payment transactions for authenticated user with pagination and filters
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getPaymentTransaction(Request $request): JsonResponse
    {
        try {
            // Validate query parameters
            $validator = Validator::make($request->query(), [
                'status' => 'nullable|string|max:255',
                'month' => 'nullable|string|regex:/^\d{4}-\d{2}$/', // Format: YYYY-MM
                'category' => 'nullable|integer|exists:categories,id',
                'per_page' => 'nullable|integer|min:1|max:100',
                'page' => 'nullable|integer|min:1',
                'search' => 'nullable|string|max:255'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid query parameters',
                    'errors' => $validator->errors()
                ], 400);
            }

            // Get authenticated user
            $user = $request->user();
            if (!$user) {
                return response()->json([
                    'success' => false,
                    'message' => 'User not authenticated'
                ], 401);
            }

            // Get query parameters
            $status = $request->query('status');
            $month = $request->query('month');
            $categoryId = $request->query('category');
            $perPage = $request->query('per_page', 50);
            $page = $request->query('page', 1);
            $search = $request->query('search');

            // Build query with relationships
            $query = PaymentTransaction::byUser($user->id)
                ->withRelations()
                ->with(['biller.category:id,cat_name']);

            // Apply filters
            if ($status !== null) {
                $query->byStatus($status);
            }

            if ($month) {
                $monthYear = explode('-', $month);
                $year = (int) $monthYear[0];
                $monthNum = (int) $monthYear[1];
                $query->byMonthYear($monthNum, $year);
            }

            if ($categoryId) {
                $query->byCategory($categoryId);
            }

            if ($search) {
                $query->where(function ($q) use ($search) {
                    $q->where('customer_name', 'LIKE', "%{$search}%")
                      ->orWhere('bill_no', 'LIKE', "%{$search}%")
                      ->orWhere('payment_ref_no', 'LIKE', "%{$search}%")
                      ->orWhere('bbps_txn_id', 'LIKE', "%{$search}%")
                      ->orWhereHas('biller', function ($billerQuery) use ($search) {
                          $billerQuery->where('biller_name', 'LIKE', "%{$search}%");
                      });
                });
            }

            // Order by created_at descending
            $query->orderBy('created_at', 'desc');

            // Get paginated results
            $transactions = $query->paginate($perPage, ['*'], 'page', $page);

            // Transform the data
            $transformedTransactions = collect($transactions->items())->map(function ($transaction) {
                return [
                    'id' => $transaction->id,
                    'customer_name' => $transaction->customer_name,
                    'bill_no' => $transaction->bill_no,
                    'bill_date' => $transaction->bill_date ? $transaction->bill_date->toDateString() : null,
                    'due_date' => $transaction->due_date ? $transaction->due_date->toDateString() : null,
                    'amount' => $transaction->amount,
                    'bbps_status' => $transaction->bbps_status,
                    'payment_ref_no' => $transaction->payment_ref_no,
                    'pg_ref_id' => $transaction->pg_ref_id,
                    'npay_pay_ref_id' => $transaction->npay_pay_ref_id,
                    'bbps_refId' => $transaction->bbps_refId,
                    'bbps_txn_id' => $transaction->bbps_txn_id,
                    'user_param1_name' => $transaction->user_param1_name,
                    'user_param1_value' => $transaction->user_param1_value,
                    'biller' => $transaction->biller ? [
                        'id' => $transaction->biller->id,
                        'biller_name' => $transaction->biller->biller_name,
                        'biller_logo' => $transaction->biller->biller_logo,
                        'category' => $transaction->biller->category ? [
                            'id' => $transaction->biller->category->id,
                            'cat_name' => $transaction->biller->category->cat_name
                        ] : null
                    ] : null,
                    'created_at' => $transaction->created_at->toISOString(),
                    'updated_at' => $transaction->updated_at ? $transaction->updated_at->toISOString() : null,
                ];
            });

            return response()->json([
                'success' => true,
                'message' => 'Payment transactions retrieved successfully',
                'data' => [
                    'transactions' => $transformedTransactions,
                    'pagination' => [
                        'current_page' => $transactions->currentPage(),
                        'per_page' => $transactions->perPage(),
                        'total' => $transactions->total(),
                        'last_page' => $transactions->lastPage(),
                        'from' => $transactions->firstItem(),
                        'to' => $transactions->lastItem(),
                        'has_more_pages' => $transactions->hasMorePages()
                    ],
                    'filters_applied' => [
                        'status' => $status,
                        'month' => $month,
                        'category' => $categoryId,
                        'search' => $search
                    ],
                    'requested_by' => $user->name
                ]
            ], 200);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve payment transactions',
                'error' => $e->getMessage()
            ], 500);
        }
    }
    /**
     * Create a new payment transaction
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function createPaymentTransaction(Request $request): JsonResponse
    {
        try {
            // Validate request
            $validator = Validator::make($request->all(), [
                'biller_id' => 'required|integer|exists:billers,id',
                'customer_name' => 'required|string|max:255',
                'bill_no' => 'required|string|max:255',
                'bill_date' => 'required|date',
                'due_date' => 'required|date|after_or_equal:bill_date',
                'amount' => 'required|numeric|min:0.01',
                'bbps_status' => 'required|string|max:255',
                'payment_ref_no' => 'required|string|max:255|unique:payment_transactions,payment_ref_no',
                'pg_ref_id' => 'required|string|max:255',
                'npay_pay_ref_id' => 'required|string|max:255',
                'bbps_refId' => 'required|string|max:255',
                'bbps_txn_id' => 'required|string|max:255|unique:payment_transactions,bbps_txn_id',
                'user_param1_name' => 'nullable|string|max:255',
                'user_param1_value' => 'nullable|string|max:255'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 400);
            }

            // Get authenticated user
            $user = $request->user();
            if (!$user) {
                return response()->json([
                    'success' => false,
                    'message' => 'User not authenticated'
                ], 401);
            }

            // Prepare data for creation
            $transactionData = $request->only([
                'biller_id',
                'customer_name',
                'bill_no',
                'bill_date',
                'due_date',
                'amount',
                'bbps_status',
                'payment_ref_no',
                'pg_ref_id',
                'npay_pay_ref_id',
                'bbps_refId',
                'bbps_txn_id',
                'user_param1_name',
                'user_param1_value'
            ]);

            // Add authenticated user ID
            $transactionData['user_id'] = $user->id;

            // Create payment transaction
            $paymentTransaction = PaymentTransaction::create($transactionData);

            // Load relationships for response
            $paymentTransaction->load(['user:id,name,email', 'biller:id,biller_name,biller_logo,category_id', 'biller.category:id,cat_name']);

            // Transform the response data
            $transformedTransaction = [
                'id' => $paymentTransaction->id,
                'customer_name' => $paymentTransaction->customer_name,
                'bill_no' => $paymentTransaction->bill_no,
                'bill_date' => $paymentTransaction->bill_date->toDateString(),
                'due_date' => $paymentTransaction->due_date->toDateString(),
                'amount' => $paymentTransaction->amount,
                'bbps_status' => $paymentTransaction->bbps_status,
                'payment_ref_no' => $paymentTransaction->payment_ref_no,
                'pg_ref_id' => $paymentTransaction->pg_ref_id,
                'npay_pay_ref_id' => $paymentTransaction->npay_pay_ref_id,
                'bbps_refId' => $paymentTransaction->bbps_refId,
                'bbps_txn_id' => $paymentTransaction->bbps_txn_id,
                'user_param1_name' => $paymentTransaction->user_param1_name,
                'user_param1_value' => $paymentTransaction->user_param1_value,
                'user' => [
                    'id' => $paymentTransaction->user->id,
                    'name' => $paymentTransaction->user->name,
                    'email' => $paymentTransaction->user->email
                ],
                'biller' => [
                    'id' => $paymentTransaction->biller->id,
                    'biller_name' => $paymentTransaction->biller->biller_name,
                    'biller_logo' => $paymentTransaction->biller->biller_logo,
                    'category' => $paymentTransaction->biller->category ? [
                        'id' => $paymentTransaction->biller->category->id,
                        'cat_name' => $paymentTransaction->biller->category->cat_name
                    ] : null
                ],
                'created_at' => $paymentTransaction->created_at->toISOString(),
                'updated_at' => $paymentTransaction->updated_at->toISOString(),
            ];

            return response()->json([
                'success' => true,
                'message' => 'Payment transaction created successfully',
                'data' => [
                    'transaction' => $transformedTransaction,
                    'created_by' => $user->name
                ]
            ], 201);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create payment transaction',
                'error' => $e->getMessage()
            ], 500);
        }
    }

}
