<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;
use App\Models\PaymentTransaction;
use Illuminate\Support\Facades\Validator;

class PaymentTransactionController extends Controller
{
    //
    public function getPaymentTransaction(Request $request){
        //Get user id with mobile number
        $user=User::where('mobile_no',$request->mobile_no)->first();
        if($user){
            $user_id=$user->id;
        }else{
           return response()->json([
                'success' => false,
                'message' => 'User not found'
            ], 404);
        }
         $txns = PaymentTransaction::select('billers.biller_name',
                                        'payment_transactions.id',
                                        'payment_transactions.bbps_status',
                                        'payment_transactions.created_at',
                                        'payment_transactions.amount')
                                    ->leftJoin('billers', 'billers.id','payment_transactions.biller_id')
                                    ->where('user_id',Auth::user()->id);
                                    // ->where('payment_transactions.bbps_status','!=',0);
        if(!empty($request->status)){

            $txns=$txns->where('payment_transactions.bbps_status',$request->status);
        }
        if(!empty($request->month)){
            $month_year = explode('-', $request->month);
            $month = $month_year[1];
            $year = $month_year[0];
            $txns=$txns->whereMonth('payment_transactions.created_at', $month)
                        ->whereYear('payment_transactions.created_at', $year);
        }
        if(!empty($request->category)){
            $txns=$txns->where('billers.category_id',$request->category);
        }
        $txns =$txns->orderBy('payment_transactions.created_at','desc')->get();
        if($payment_transactions){
            return response()->json([
                'success' => true,
                'message' => 'Payment transactions retrieved successfully',
                'data' => $payment_transactions
            ], 200);
        }else{
            return response()->json([
                'success' => false,
                'message' => 'Payment transactions not found'
            ], 404);
        }
    }
    //Create payment transaction
    public function createPaymentTransaction(Request $request){
        //validate request
        $validator = Validator::make($request->all(), [
            'user_id' => 'required|exists:users,id',
            'customer_name' => 'required|string|max:255',
            'bill_no' => 'required|string|max:255',
            'bill_date' => 'required|date',
            'due_date' => 'required|date',
            'amount' => 'required|numeric',
            'bbps_status' => 'required|string|max:255',
            'payment_ref_no' => 'required|string|max:255',
            'pg_ref_id' => 'required|string|max:255',
            'npay_pay_ref_id' => 'required|string|max:255',
            'bbps_refId' => 'required|string|max:255',
            'bbps_txn_id' => 'required|string|max:255',
            'user_param1_name' => 'required|string|max:255',
            'user_param1_value' => 'required|string|max:255'
        ]);
        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 400);
        }
        //Create payment transaction
        $payment_transaction=PaymentTransaction::create($request->all());
        if($payment_transaction){
            return response()->json([
                'success' => true,
                'message' => 'Payment transaction created successfully',
                'data' => $payment_transaction
            ], 201);
        }else{
            return response()->json([
                'success' => false,
                'message' => 'Payment transaction creation failed'
            ], 500);
        }
    }

}
