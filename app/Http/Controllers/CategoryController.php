<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use App\Models\Category;

class CategoryController extends Controller
{
    /**
     * Get all categories list
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        try {
            // Get query parameters for filtering
            $status = $request->query('status');
            $withBillers = $request->query('with_billers', false);
            $perPage = $request->query('per_page', 50); // Default 50 items per page
            $page = $request->query('page', 1);

            // Validate query parameters
            $validator = Validator::make($request->query(), [
                'status' => 'nullable|in:0,1',
                'with_billers' => 'nullable|boolean',
                'per_page' => 'nullable|integer|min:1|max:100',
                'page' => 'nullable|integer|min:1'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid query parameters',
                    'errors' => $validator->errors()
                ], 400);
            }

            // Build query
            $query = Category::query();

            // Apply filters
            if ($status !== null) {
                $query->where('status', $status);
            }

            if ($withBillers) {
                $query->withBillers();
            }

            // Order by category name
            $query->orderBy('cat_name', 'asc');

            // Get paginated results
            $categories = $query->paginate($perPage, ['*'], 'page', $page);

            // Transform the data
            $transformedCategories = collect($categories->items())->map(function ($category) {
                return [
                    'id' => $category->id,
                    'cat_name' => $category->cat_name,
                    'cat_logo' => $category->cat_logo,
                    'biller_count' => $category->biller_count ?? 0,
                    'status' => $category->status,
                    'status_text' => $category->status == 1 ? 'Active' : 'Inactive',
                    'created_at' => $category->created_at->toISOString(),
                    'updated_at' => $category->updated_at->toISOString(),
                ];
            });

            return response()->json([
                'success' => true,
                'message' => 'Categories retrieved successfully',
                'data' => [
                    'categories' => $transformedCategories,
                    'pagination' => [
                        'current_page' => $categories->currentPage(),
                        'per_page' => $categories->perPage(),
                        'total' => $categories->total(),
                        'last_page' => $categories->lastPage(),
                        'from' => $categories->firstItem(),
                        'to' => $categories->lastItem(),
                        'has_more_pages' => $categories->hasMorePages()
                    ],
                    'filters_applied' => [
                        'status' => $status,
                        'with_billers' => $withBillers
                    ],
                    'requested_by' => $request->user()->name
                ]
            ], 200);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve categories',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get active categories only
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function active(Request $request): JsonResponse
    {
        try {
            $withBillers = $request->query('with_billers', false);

            // Build query for active categories
            $query = Category::active();

            if ($withBillers) {
                $query->withBillers();
            }

            $categories = $query->orderBy('cat_name', 'asc')->get();

            // Transform the data
            $transformedCategories = $categories->map(function ($category) {
                return [
                    'id' => $category->id,
                    'cat_name' => $category->cat_name,
                    'cat_logo' => $category->cat_logo,
                    'biller_count' => $category->biller_count ?? 0,
                    'created_at' => $category->created_at->toISOString(),
                    'updated_at' => $category->updated_at->toISOString(),
                ];
            });

            return response()->json([
                'success' => true,
                'message' => 'Active categories retrieved successfully',
                'data' => [
                    'categories' => $transformedCategories,
                    'total_count' => $categories->count(),
                    'filters_applied' => [
                        'status' => 'active',
                        'with_billers' => $withBillers
                    ],
                    'requested_by' => $request->user()->name
                ]
            ], 200);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve active categories',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get category by ID
     *
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     */
    public function show(Request $request, int $id): JsonResponse
    {
        try {
            $category = Category::find($id);

            if (!$category) {
                return response()->json([
                    'success' => false,
                    'message' => 'Category not found'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'message' => 'Category retrieved successfully',
                'data' => [
                    'category' => [
                        'id' => $category->id,
                        'cat_name' => $category->cat_name,
                        'cat_logo' => $category->cat_logo,
                        'biller_count' => $category->biller_count ?? 0,
                        'status' => $category->status,
                        'status_text' => $category->status == 1 ? 'Active' : 'Inactive',
                        'created_at' => $category->created_at->toISOString(),
                        'updated_at' => $category->updated_at->toISOString(),
                    ],
                    'requested_by' => $request->user()->name
                ]
            ], 200);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve category',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get categories statistics
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function stats(Request $request): JsonResponse
    {
        try {
            $totalCategories = Category::count();
            $activeCategories = Category::active()->count();
            $inactiveCategories = Category::where('status', 0)->count();
            $categoriesWithBillers = Category::withBillers()->count();
            $totalBillers = Category::sum('biller_count');

            return response()->json([
                'success' => true,
                'message' => 'Categories statistics retrieved successfully',
                'data' => [
                    'statistics' => [
                        'total_categories' => $totalCategories,
                        'active_categories' => $activeCategories,
                        'inactive_categories' => $inactiveCategories,
                        'categories_with_billers' => $categoriesWithBillers,
                        'total_billers' => $totalBillers ?? 0,
                        'average_billers_per_category' => $totalCategories > 0 ? round(($totalBillers ?? 0) / $totalCategories, 2) : 0
                    ],
                    'requested_by' => $request->user()->name,
                    'generated_at' => now()->toISOString()
                ]
            ], 200);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve categories statistics',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
