<?php

namespace App\Http\Controllers;

use stdClass;
use App\Models\Biller;
use App\Models\Category;
use Illuminate\Http\Request;
use App\Models\BbpsBillFetchLog;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\Validator;

class BillerController extends Controller
{
    /**
     * Get billers list by category ID
     *
     * @param Request $request
     * @param int $categoryId
     * @return JsonResponse
     */
    public function getBillersByCategory(Request $request, int $categoryId): JsonResponse
    {
        try {
            // Validate query parameters
            $validator = Validator::make($request->query(), [
                'status' => 'nullable|in:0,1',
                'per_page' => 'nullable|integer|min:1|max:100',
                'page' => 'nullable|integer|min:1',
                'search' => 'nullable|string|max:255'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid query parameters',
                    'errors' => $validator->errors()
                ], 400);
            }

            // Check if category exists
            $category = Category::find($categoryId);
            if (!$category) {
                return response()->json([
                    'success' => false,
                    'message' => 'Category not found'
                ], 404);
            }

            // Get query parameters
            $status = $request->query('status');
            $perPage = $request->query('per_page', 50);
            $page = $request->query('page', 1);
            $search = $request->query('search');

            // Build query
            $query = Biller::byCategory($categoryId)->with('category:id,cat_name');

            // Apply filters
            if ($status !== null) {
                $query->where('status', $status);
            }

            if ($search) {
                $query->where(function ($q) use ($search) {
                    $q->where('biller_name', 'LIKE', "%{$search}%")
                      ->orWhere('bbps_id', 'LIKE', "%{$search}%");
                });
            }

            // Order by biller name
            $query->orderBy('biller_name', 'asc');

            // Get paginated results
            $billers = $query->paginate($perPage, ['*'], 'page', $page);

            // Transform the data
            $transformedBillers = collect($billers->items())->map(function ($biller) {
                return [
                    'id' => $biller->id,
                    'bbps_id' => $biller->bbps_id,
                    'biller_name' => $biller->biller_name,
                    'biller_logo' => $biller->biller_logo,
                    'status' => $biller->status,

                ];
            });

            return response()->json([
                'success' => true,
                'message' => 'Billers retrieved successfully',
                'data' => [
                    'category' => [
                        'id' => $category->id,
                        'cat_name' => $category->cat_name,
                        'status' => $category->status
                    ],
                    'billers' => $transformedBillers,
                    'pagination' => [
                        'current_page' => $billers->currentPage(),
                        'per_page' => $billers->perPage(),
                        'total' => $billers->total(),
                        'last_page' => $billers->lastPage(),
                        'from' => $billers->firstItem(),
                        'to' => $billers->lastItem(),
                        'has_more_pages' => $billers->hasMorePages()
                    ],
                    'filters_applied' => [
                        'category_id' => $categoryId,
                        'status' => $status,
                        'search' => $search
                    ],
                    'requested_by' => $request->user()->name
                ]
            ], 200);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve billers',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get biller detail by ID
     *
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     */
    public function getBillerDetail(Request $request, int $id): JsonResponse
    {
        try {
            // Find biller with category relationship
            $biller = Biller::with('category:id,cat_name,cat_logo,status')->find($id);

            if (!$biller) {
                return response()->json([
                    'success' => false,
                    'message' => 'Biller not found'
                ], 404);
            }

            // Prepare detailed biller data
            $billerDetail = [
                'id' => $biller->id,
                'category_id' => $biller->category_id,
                'category' => [
                    'id' => $biller->category->id,
                    'cat_name' => $biller->category->cat_name,
                    'cat_logo' => $biller->category->cat_logo,
                    'status' => $biller->category->status,
                    'status_text' => $biller->category->status == 1 ? 'Active' : 'Inactive'
                ],
                'bbps_id' => $biller->bbps_id,
                'biller_name' => $biller->biller_name,
                'biller_logo' => $biller->biller_logo,
                'band_color' => $biller->band_color,
                'fetch_param' => $biller->fetch_param, // This will be cast to array
                'biller_config' => $biller->biller_config, // This will be cast to array
                'status' => $biller->status,
                'status_text' => $biller->status == 1 ? 'Active' : 'Inactive',
                'created_at' => $biller->created_at->toISOString(),
                'updated_at' => $biller->updated_at ? $biller->updated_at->toISOString() : null,
            ];

            return response()->json([
                'success' => true,
                'message' => 'Biller detail retrieved successfully',
                'data' => [
                    'biller' => $billerDetail,
                    'requested_by' => $request->user()->name
                ]
            ], 200);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve biller detail',
                'error' => $e->getMessage()
            ], 500);
        }
    }
      public function fetchBillData(Request $request){
        $billParameters = [];

        if(env('LIVE_MODE', false)){
            $biller = Biller::find($request->biller_id);
            // dd( $biller);
            $biller_id=$biller->bbps_id;
            $biller_label=$biller->fetch_param;
            // $customer_id=$request->bill_no;
            $ip=$request->ip();
            $user_mobile=$request->mobile_no;
            //dd($request->user_params);
            foreach($request->user_params as $user_param){
                $billParameters[] = [
                    'name' => $user_param['paramName'],
                    'value' => $user_param['user_value']
                ];
            }
        }else{
            $biller_id="ACT000000NAT01";
            $biller_label="Account Number/User Name";
            $customer_id="************";
            $ip="*************";
            $user_mobile="**********";
            $billParameters[] = [
                'name' => $biller_label,
                'value' => $customer_id
            ];
        }





         //dd(json_encode($billParameters));
        // "billParameters": '.json_encode($billParameters).',
        // dd($billParameters[0]['value']);
        $tokenData=$this->getSetuToken();
        //dd($tokenData);
        if ($tokenData && isset($tokenData['success'])) {
            $token=$tokenData['token'];
            $input_data='{
                "agent": {
                    "channel": "INT",
                    "id": "********************",
                    "app": "",
                    "geocode": "",
                    "ifsc": "",
                    "imei": "",
                    "ip": "*************",
                    "mac": "A4-97-B1-43-91-46",
                    "mobile": "",
                    "os": "",
                    "postalCode": "",
                    "terminalId": ""
                },
                "biller": {
                    "id": "'.$biller_id.'"
                },
                "customer": {
                    "billParameters": '.json_encode($billParameters).',
                    "mobile": "9666845678"
                },
                "autoFetch": false
                }';
            $curl = curl_init();

            curl_setopt_array($curl, array(
            CURLOPT_URL => env('BBPS_API_URL').'/bbps/bills/fetch/request',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_SSL_VERIFYPEER=>false,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS =>$input_data,
            CURLOPT_HTTPHEADER => array(
                'X-PARTNER-ID: '.env('BBPS_X_PARTNER_ID'),
                'Content-Type: application/json',
                'Accept: application/json',
                'Authorization: Bearer '.$token
            ),
            ));

            $response = curl_exec($curl);
            Log::channel('bbps')->info('Bill Fetch Request inpuput:'. $input_data);
            Log::channel('bbps')->info('Bill Fetch Request Respones:'. $response);
            curl_close($curl);
            $responseArray = json_decode($response, true);
            $return_response=$responseArray;
            if ($responseArray && isset($responseArray['success']) && @$responseArray['success']) {

                BbpsBillFetchLog::create(['ref_id'=>$responseArray['data']['refId'],'request'=>$input_data,'user_param1_name'=>$billParameters[0]['name'],'user_param1_value'=>$billParameters[0]['value']]);
                // $refId=$responseArray['data']['refId'];
                // $return_response['refId']=$refId;
            // $curl2 = curl_init();

            // curl_setopt_array($curl2, array(
            // CURLOPT_URL => env('BBPS_API_URL').'/bbps/bills/fetch/response',
            // CURLOPT_RETURNTRANSFER => true,
            // CURLOPT_ENCODING => '',
            // CURLOPT_MAXREDIRS => 10,
            // CURLOPT_TIMEOUT => 0,
            // CURLOPT_FOLLOWLOCATION => true,
            // CURLOPT_SSL_VERIFYPEER=>false,
            // CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            // CURLOPT_CUSTOMREQUEST => 'POST',
            // CURLOPT_POSTFIELDS =>'{
            // "refId": "'.$refId.'"
            // }',
            // CURLOPT_HTTPHEADER => array(
            //     'X-PARTNER-ID: '.env('BBPS_X_PARTNER_ID'),
            //     'Content-Type: application/json',
            //     'Accept: application/json',
            //     'Authorization: Bearer '.$token
            // ),
            // ));

            //     $response2 = curl_exec($curl2);
            //     $response1= '{
            //         "data": {
            //           "bill": {
            //             "amount": 100,
            //             "billDate": "2020-12-01",
            //             "billNumber": "1608806543",
            //             "billPeriod": "MONTHLY",
            //             "customerName": "Test 5 ",
            //             "dueDate": "2020-12-07"
            //           },
            //           "billerRefId": "********",
            //           "exactness": "Exact",
            //           "paymentLimits": [
            //             {
            //               "maxLimit": *********,
            //               "minLimit": 100,
            //               "paymentMode": "Internet Banking",
            //               "supportsPendingStatus": false
            //             },
            //             {
            //               "maxLimit": *********,
            //               "minLimit": 100,
            //               "paymentMode": "Debit Card",
            //               "supportsPendingStatus": false
            //             },
            //             {
            //               "maxLimit": *********,
            //               "minLimit": 100,
            //               "paymentMode": "Credit Card",
            //               "supportsPendingStatus": false
            //             },
            //             {
            //               "maxLimit": *********,
            //               "minLimit": 100,
            //               "paymentMode": "Prepaid Card",
            //               "supportsPendingStatus": false
            //             },
            //             {
            //               "maxLimit": *********,
            //               "minLimit": 100,
            //               "paymentMode": "IMPS",
            //               "supportsPendingStatus": false
            //             },
            //             {
            //               "maxLimit": 4999900,
            //               "minLimit": 100,
            //               "paymentMode": "Cash",
            //               "supportsPendingStatus": false
            //             },
            //             {
            //               "maxLimit": *********,
            //               "minLimit": 100,
            //               "paymentMode": "UPI",
            //               "supportsPendingStatus": false
            //             },
            //             {
            //               "maxLimit": *********,
            //               "minLimit": 100,
            //               "paymentMode": "Wallet",
            //               "supportsPendingStatus": false
            //             },
            //             {
            //               "maxLimit": *********,
            //               "minLimit": 100,
            //               "paymentMode": "NEFT",
            //               "supportsPendingStatus": false
            //             },
            //             {
            //               "maxLimit": *********,
            //               "minLimit": 100,
            //               "paymentMode": "AEPS",
            //               "supportsPendingStatus": false
            //             },
            //             {
            //               "maxLimit": *********,
            //               "minLimit": 100,
            //               "paymentMode": "Account Transfer",
            //               "supportsPendingStatus": false
            //             }
            //           ],
            //           "refId": "COS90VGUO0KQRSK2OPT0GK9K03241271340",
            //           "status": "Success"
            //         },
            //         "success": true,
            //         "traceId": "COS90VGUO0KQRSK2OPUG"
            //       }';
            //     curl_close($curl2);
            //     Log::channel('bbps')->info('Bill Fetch Response Response:'. $response2);
            //     $response2 = \json_decode($response2);
            //     // dd($response);
            //     if($response2->success){
            //         $return_response['status']=1;
            //         $return_response['bill_data']=$response2;
                    // if($response2->data->status=="Success"){

                    //     $return_response['bill_data']=new stdClass();
                    //     if($response2->data->exactness!="Any"){
                    //         $response2->data->bill->bbpsrefid = $response2->data->refId;
                    //         $response2->data->bill->bill_amount = $response2->data->bill->amount/100;
                    //         $return_response['bill_data']=$response2->data->bill;
                    //         $return_response['bill_data']->amount=$response2->data->bill->amount/100;
                    //        }
                    //         $return_response['status']=1;
                    //         $return_response['message']='success';
                    //         $return_response['bill_data']->exactness = $response2->data->exactness;
                    //         $return_response['org_response']=$response2;
                    //         BbpsBillFetchLog::where('ref_id',$response2->data->refId)->update(['response'=>$response2]);

                    // }else if($response2->data->status=="Processing"){
                    //     $return_response['status']=2;
                    //     $return_response['message']='Pending will retry';
                    //     // $return_response['org_status']=$response2->data->status;
                    //     // $return_response['org_response']=$response2;
                    //     $return_response['refId']=$response2->data->refId;
                    // }else if($response2->data->status=="Failure"){
                    //     $return_response['status']=3;
                    //     $return_response['message']=$response2->data->failureReason->message;;
                    //     $return_response['org_status']=$response2->data->status;
                    //     $return_response['org_response']=$response2;

                    // }else{
                    //     $return_response['status']=4;
                    //     $return_response['message']='Pending will retry';
                    //     $return_response['org_status']=$response2->data->status;
                    //     $return_response['org_response']=$response2;

                    // }
               // }

            }

        }else{
        $return_response['status']=false;
        $return_response['message']="Token generation failed";
        }
        return Response::json($return_response);
    }
     public function getfetchbillstatus($refId){
        $tokenData=$this->getSetuToken();
        if ($tokenData && isset($tokenData['success'])) {
            $token=$tokenData['token'];
            //$refId=$request->refId;

            $curl2 = curl_init();

            curl_setopt_array($curl2, array(
            CURLOPT_URL => env('BBPS_API_URL').'/bbps/bills/fetch/response',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_SSL_VERIFYPEER=>false,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS =>'{
            "refId": "'.$refId.'"
            }',
            CURLOPT_HTTPHEADER => array(
                'X-PARTNER-ID: '.env('BBPS_X_PARTNER_ID'),
                'Content-Type: application/json',
                'Accept: application/json',
                'Authorization: Bearer '.$token
            ),
            ));

                $response2 = curl_exec($curl2);

                curl_close($curl2);
                Log::channel('bbps')->info('Bill Fetch Status:'. $response2);
                $response2 = \json_decode($response2);
               $return_response=$response2;



        }else{
        $return_response['status']=false;
        $return_response['message']="Token generation failed";
        }
        return Response::json($return_response);
    }
      public function getSetuToken(){
        $curl = curl_init();
        curl_setopt_array($curl, array(
        CURLOPT_URL => env('BBPS_TOKEN_API_URL').'/auth/token',
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING => '',
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 0,
        CURLOPT_SSL_VERIFYPEER=>false,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => 'POST',
        CURLOPT_POSTFIELDS =>'{  "clientID": "'.env('BBPS_CLIENTID').'",  "secret": "'.env('BBPS_SECRET').'"}',
        CURLOPT_HTTPHEADER => array(
            'X-PARTNER-ID: '.env('BBPS_X_PARTNER_ID'),
            'Content-Type: application/json'
        ),
        ));
        if(curl_exec($curl) === false)
        {
            echo 'Curl error: ' . curl_error($curl);
        }
        $response = curl_exec($curl);

        curl_close($curl);

        $responseArray = json_decode($response, true);


        return  $responseArray ;
    }
}
