<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use App\Models\User;

class AuthController extends Controller
{

    /**
     * Generate access token using fixed API key
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function generateToken(Request $request): JsonResponse
    {
        try {
            // Validate the request
            $validator = Validator::make($request->all(), [
                'api_key' => 'required|string',
                'client_name' => 'string|max:255'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 400);
            }

            $apiKey = $request->input('api_key');
            $clientName = $request->input('client_name', config('bbps.token.default_name'));

            // Get valid API keys from config
            $validApiKeys = config('bbps.fixed_api_keys');

            // Check if the provided API key is valid
            if (!in_array($apiKey, $validApiKeys)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid API key provided'
                ], 401);
            }

            // Create or find a system user for token generation
            $systemEmail = config('bbps.system_user.email');
            $systemName = config('bbps.system_user.name');

            $user = User::firstOrCreate(
                ['email' => $systemEmail],
                [
                    'name' => $systemName,
                    'email' => $systemEmail,
                    'mobile_no' => '0000000000', // Default mobile number for system user
                    'password' => Hash::make('system_password_' . time()),
                    'email_verified_at' => now()
                ]
            );

            // Generate token with abilities/scopes
            $tokenAbilities = config('bbps.token.abilities');
            $token = $user->createToken($clientName, $tokenAbilities);

            return response()->json([
                'success' => true,
                'message' => 'Token generated successfully',
                'data' => [
                    'access_token' => $token->plainTextToken,
                    'token_type' => 'Bearer',
                    'expires_at' => null, // Tokens don't expire by default in Sanctum
                    'client_name' => $clientName
                ]
            ], 200);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Token generation failed',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Validate and get token information
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function validateToken(Request $request): JsonResponse
    {
        try {
            $user = $request->user();

            if (!$user) {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid or expired token'
                ], 401);
            }

            return response()->json([
                'success' => true,
                'message' => 'Token is valid',
                'data' => [
                    'user_id' => $user->id,
                    'user_name' => $user->name,
                    'user_email' => $user->email,
                    'token_name' => $request->user()->currentAccessToken()->name ?? 'Unknown',
                    'abilities' => $request->user()->currentAccessToken()->abilities ?? []
                ]
            ], 200);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Token validation failed',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Revoke current token
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function revokeToken(Request $request): JsonResponse
    {
        try {
            // Revoke the current access token
            $request->user()->tokens()->where('id', $request->user()->currentAccessToken()->id)->delete();

            return response()->json([
                'success' => true,
                'message' => 'Token revoked successfully'
            ], 200);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Token revocation failed',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get all valid API keys (for development/testing purposes)
     * Remove this method in production
     *
     * @return JsonResponse
     */
    public function getValidKeys(): JsonResponse
    {
        if (app()->environment('production')) {
            return response()->json([
                'success' => false,
                'message' => 'This endpoint is not available in production'
            ], 403);
        }

        return response()->json([
            'success' => true,
            'message' => 'Valid API keys for development',
            'data' => [
                'valid_keys' => config('bbps.fixed_api_keys'),
                'note' => 'Use any of these keys to generate access tokens'
            ]
        ], 200);
    }
}
