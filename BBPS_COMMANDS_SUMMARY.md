# BBPS Sync Commands - Quick Reference

## 🎯 **Two Commands Available**

### 1. **Categories Sync** (Run First)
```bash
php artisan bbps:sync-categories
```
- **Purpose:** Sync category master data from BBPS API
- **Data:** ~50-100 categories
- **Time:** Few seconds
- **Frequency:** Weekly/Monthly

### 2. **Billers Sync** (Run Second)
```bash
php artisan bbps:sync-billers
```
- **Purpose:** Sync individual biller records for each category
- **Data:** Thousands of billers
- **Time:** Several minutes (optimized for bulk processing)
- **Frequency:** Daily

---

## 🔄 **Complete Sync Workflow**

### Daily Sync (Recommended)
```bash
# Step 1: Sync categories first
php artisan bbps:sync-categories

# Step 2: Sync billers second
php artisan bbps:sync-billers
```

### Quick Category-Specific Sync
```bash
# Sync only specific category billers
php artisan bbps:sync-billers --category-id=1
```

---

## 📋 **Categories Command Options**

```bash
# Basic sync
php artisan bbps:sync-categories

# Force sync even if no categories returned
php artisan bbps:sync-categories --force
```

---

## 🏢 **Billers Command Options**

```bash
# Sync all billers
php artisan bbps:sync-billers

# Sync specific category
php artisan bbps:sync-billers --category-id=1
php artisan bbps:sync-billers --category-name="Electricity"

# Performance tuning (max batch size: 250)
php artisan bbps:sync-billers --batch-size=250 --delay=2

# Force sync empty categories
php artisan bbps:sync-billers --force

# Skip invalid categories automatically
php artisan bbps:sync-billers --skip-invalid

# Validate which categories are valid before syncing
php artisan bbps:sync-billers --validate-categories
```

---

## ⚡ **Performance Tips**

### For Large Datasets
```bash
# Use maximum batch size and delay for better performance
php artisan bbps:sync-billers --batch-size=250 --delay=2
```

### For Specific Categories
```bash
# Process only categories that need updates
php artisan bbps:sync-billers --category-name="Mobile"
```

---

## 🔍 **Monitoring**

### Check Available Commands
```bash
php artisan list | grep bbps
```

### View Help
```bash
php artisan bbps:sync-categories --help
php artisan bbps:sync-billers --help
```

### Check Logs
```bash
tail -f storage/logs/bbps.log
```

---

## 🚨 **Troubleshooting**

### Authentication Issues
```
❌ Failed to get authentication token
```
**Solution:** Check `.env` file for correct API credentials

### No Categories Found
```
⚠️ No categories found to process
```
**Solution:** Run categories sync first: `php artisan bbps:sync-categories`

### Invalid Category Names
```
❌ Error processing batch 1: HTTP Error: 400 - Response: {"error":{"code":"invalid-category-param"...
```
**Solutions:**
- Use `--skip-invalid` to automatically skip invalid categories
- Use `--validate-categories` to check which categories are valid first
- Update category names in database to match API expectations

### Memory Issues
```
Fatal error: Allowed memory size exhausted
```
**Solution:** Commands automatically handle memory optimization

---

## 📅 **Scheduling (Optional)**

Add to your crontab for automated syncing:

```bash
# Daily sync at 2 AM
0 2 * * * cd /path/to/project && php artisan bbps:sync-categories
5 2 * * * cd /path/to/project && php artisan bbps:sync-billers
```

---

## ✅ **Migration from Old Method**

**Before (HTTP Controller):**
```bash
curl -X POST /api/sync-categories
curl -X POST /api/sync-billers
```

**Now (Artisan Commands):**
```bash
php artisan bbps:sync-categories
php artisan bbps:sync-billers
```

**Benefits:**
- ✅ 10x faster processing
- ✅ Better memory management  
- ✅ Progress tracking
- ✅ Error recovery
- ✅ Flexible filtering
- ✅ Background processing capability
